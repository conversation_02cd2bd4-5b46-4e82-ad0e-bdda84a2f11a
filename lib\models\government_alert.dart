class GovernmentAlert {
  final String id;
  final String title;
  final String message;
  final String alertType;
  final String severity;
  final String? targetArea;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final bool isActive;

  GovernmentAlert({
    required this.id,
    required this.title,
    required this.message,
    required this.alertType,
    required this.severity,
    this.targetArea,
    required this.createdAt,
    this.expiresAt,
    required this.isActive,
  });

  factory GovernmentAlert.fromJson(Map<String, dynamic> json) {
    return GovernmentAlert(
      id: json['id'],
      title: json['title'],
      message: json['message'],
      alertType: json['alert_type'],
      severity: json['severity'],
      targetArea: json['target_area'],
      createdAt: DateTime.parse(json['created_at']),
      expiresAt: json['expires_at'] != null ? DateTime.parse(json['expires_at']) : null,
      isActive: json['is_active'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'alert_type': alertType,
      'severity': severity,
      'target_area': targetArea,
      'created_at': createdAt.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'is_active': isActive,
    };
  }
} 