import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/government_service.dart';
import '../../models/government_user.dart';
import 'camp_management_screen.dart';
import 'alert_management_screen.dart';
import 'disaster_management_screen.dart';
import 'user_tracking_screen.dart';

class GovernmentDashboard extends ConsumerStatefulWidget {
  const GovernmentDashboard({super.key});

  @override
  ConsumerState<GovernmentDashboard> createState() => _GovernmentDashboardState();
}

class _GovernmentDashboardState extends ConsumerState<GovernmentDashboard> {
  GovernmentUser? governmentUser;
  Map<String, dynamic> dashboardStats = {};
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() => isLoading = true);
    
    try {
      final user = await GovernmentService.getCurrentGovernmentUser();
      final stats = await GovernmentService.getDashboardStats();
      
      setState(() {
        governmentUser = user;
        dashboardStats = stats;
        isLoading = false;
      });
    } catch (e) {
      setState(() => isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading dashboard: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (governmentUser == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Access Denied')),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.security, size: 64, color: Colors.red),
              SizedBox(height: 16),
              Text(
                'Government Access Required',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('You do not have government user privileges.'),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Emergency Management Dashboard'),
        backgroundColor: Colors.red.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDashboardData,
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadDashboardData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildUserInfo(),
              const SizedBox(height: 24),
              _buildStatsCards(),
              const SizedBox(height: 24),
              _buildQuickActions(),
              const SizedBox(height: 24),
              _buildManagementSections(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              backgroundColor: Colors.red.shade700,
              child: const Icon(Icons.admin_panel_settings, color: Colors.white),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    governmentUser!.departmentDisplayName,
                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    'Clearance Level: ${governmentUser!.clearanceLevelName}',
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
            if (governmentUser!.canActivateDisasters)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.orange.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  'DISASTER AUTH',
                  style: TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsCards() {
    return Row(
      children: [
        Expanded(child: _buildStatCard('Active Disasters', dashboardStats['active_disasters'] ?? 0, Icons.warning, Colors.red)),
        const SizedBox(width: 8),
        Expanded(child: _buildStatCard('Active Camps', dashboardStats['active_camps'] ?? 0, Icons.cabin, Colors.green)),
        const SizedBox(width: 8),
        Expanded(child: _buildStatCard('Active Alerts', dashboardStats['active_alerts'] ?? 0, Icons.notifications, Colors.orange)),
        const SizedBox(width: 8),
        Expanded(child: _buildStatCard('Tracked Users', dashboardStats['tracked_users'] ?? 0, Icons.people, Colors.blue)),
      ],
    );
  }

  Widget _buildStatCard(String title, int value, IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              value.toString(),
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Actions',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            if (governmentUser!.canActivateDisasters) ...[
              Expanded(
                child: _buildActionButton(
                  'Activate Disaster',
                  Icons.warning,
                  Colors.red,
                  () => _showActivateDisasterDialog(),
                ),
              ),
              const SizedBox(width: 8),
            ],
            Expanded(
              child: _buildActionButton(
                'Send Alert',
                Icons.campaign,
                Colors.orange,
                () => _showSendAlertDialog(),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildActionButton(
                'Emergency Call',
                Icons.phone,
                Colors.green,
                () => _makeEmergencyCall(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton(String title, IconData icon, Color color, VoidCallback onPressed) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.all(16),
      ),
      child: Column(
        children: [
          Icon(icon, size: 24),
          const SizedBox(height: 4),
          Text(title, style: const TextStyle(fontSize: 12)),
        ],
      ),
    );
  }

  Widget _buildManagementSections() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Management',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        _buildManagementTile(
          'Disaster Management',
          'Monitor and manage active disasters',
          Icons.terrain,
          Colors.red,
          () => Navigator.push(context, MaterialPageRoute(builder: (_) => const DisasterManagementScreen())),
        ),
        if (governmentUser!.canManageCamps)
          _buildManagementTile(
            'Camp Management',
            'Manage rescue camps and resources',
            Icons.cabin,
            Colors.green,
            () => Navigator.push(context, MaterialPageRoute(builder: (_) => const CampManagementScreen())),
          ),
        if (governmentUser!.canManageAlerts)
          _buildManagementTile(
            'Alert Management',
            'Create and manage emergency alerts',
            Icons.notifications,
            Colors.orange,
            () => Navigator.push(context, MaterialPageRoute(builder: (_) => const AlertManagementScreen())),
          ),
        if (governmentUser!.canViewAllUsers)
          _buildManagementTile(
            'User Tracking',
            'Monitor citizen locations during emergencies',
            Icons.people,
            Colors.blue,
            () => Navigator.push(context, MaterialPageRoute(builder: (_) => const UserTrackingScreen())),
          ),
      ],
    );
  }

  Widget _buildManagementTile(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: color.withOpacity(0.1),
          child: Icon(icon, color: color),
        ),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }

  void _showActivateDisasterDialog() {
    // TODO: Implement disaster activation dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Activate Disaster'),
        content: const Text('Disaster activation dialog will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _showSendAlertDialog() {
    // TODO: Implement send alert dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Send Emergency Alert'),
        content: const Text('Alert sending dialog will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _makeEmergencyCall() {
    // TODO: Implement emergency call functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Emergency call functionality will be implemented')),
    );
  }
}
