import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../services/government_service.dart';
import '../../models/government_user.dart';
import 'disaster_management_screen.dart';
import 'camp_management_screen.dart';
import 'alert_management_screen.dart';
import 'user_tracking_screen.dart';

class GovernmentDashboard extends ConsumerWidget {
  const GovernmentDashboard({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Government Dashboard'),
      ),
      body: GridView.count(
        crossAxisCount: 2,
        padding: const EdgeInsets.all(16),
        mainAxisSpacing: 16,
        crossAxisSpacing: 16,
        children: [
          _buildDashboardCard(
            context,
            'Disaster Management',
            Icons.warning_rounded,
            () => Navigator.push(
              context,
              MaterialPageRoute(builder: (_) => const DisasterManagementScreen()),
            ),
          ),
          _buildDashboardCard(
            context,
            'Camp Management',
            Icons.cabin_rounded,
            () => Navigator.push(
              context,
              MaterialPageRoute(builder: (_) => const CampManagementScreen()),
            ),
          ),
          _buildDashboardCard(
            context,
            'Alert Management',
            Icons.notifications_active_rounded,
            () => Navigator.push(
              context,
              MaterialPageRoute(builder: (_) => const AlertManagementScreen()),
            ),
          ),
          _buildDashboardCard(
            context,
            'User Tracking',
            Icons.location_on_rounded,
            () => Navigator.push(
              context,
              MaterialPageRoute(builder: (_) => const UserTrackingScreen()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardCard(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 48, color: Theme.of(context).colorScheme.primary),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
