import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/government_service.dart';

class UserTrackingScreen extends ConsumerStatefulWidget {
  const UserTrackingScreen({super.key});

  @override
  ConsumerState<UserTrackingScreen> createState() => _UserTrackingScreenState();
}

class _UserTrackingScreenState extends ConsumerState<UserTrackingScreen> {
  List<Map<String, dynamic>> userLocations = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadUserLocations();
  }

  Future<void> _loadUserLocations() async {
    setState(() => isLoading = true);
    try {
      final locations = await GovernmentService.getUserLocations();
      setState(() {
        userLocations = locations;
        isLoading = false;
      });
    } catch (e) {
      setState(() => isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading user locations: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Tracking'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadUserLocations,
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadUserLocations,
              child: userLocations.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.location_off, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text(
                            'No Active Tracking',
                            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                          ),
                          Text('No users are currently being tracked'),
                        ],
                      ),
                    )
                  : Column(
                      children: [
                        _buildStatsHeader(),
                        Expanded(
                          child: ListView.builder(
                            padding: const EdgeInsets.all(16),
                            itemCount: userLocations.length,
                            itemBuilder: (context, index) {
                              final location = userLocations[index];
                              return _buildUserLocationCard(location);
                            },
                          ),
                        ),
                      ],
                    ),
            ),
    );
  }

  Widget _buildStatsHeader() {
    final safeCount = userLocations.where((loc) => loc['status'] == 'safe').length;
    final needsHelpCount = userLocations.where((loc) => loc['status'] == 'needs_help').length;
    final noResponseCount = userLocations.where((loc) => loc['status'] == 'no_response').length;

    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey.shade100,
      child: Row(
        children: [
          Expanded(child: _buildStatItem('Total', userLocations.length, Colors.blue)),
          Expanded(child: _buildStatItem('Safe', safeCount, Colors.green)),
          Expanded(child: _buildStatItem('Needs Help', needsHelpCount, Colors.red)),
          Expanded(child: _buildStatItem('No Response', noResponseCount, Colors.orange)),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, int count, Color color) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildUserLocationCard(Map<String, dynamic> location) {
    final user = location['users'] as Map<String, dynamic>;
    final status = location['status'] as String;
    final lastUpdated = DateTime.parse(location['last_updated'] as String);
    final address = location['address'] as String?;
    final batteryLevel = location['battery_level'] as int?;

    Color statusColor;
    IconData statusIcon;
    switch (status) {
      case 'safe':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'needs_help':
        statusColor = Colors.red;
        statusIcon = Icons.help;
        break;
      case 'unsafe':
        statusColor = Colors.orange;
        statusIcon = Icons.warning;
        break;
      default:
        statusColor = Colors.grey;
        statusIcon = Icons.help_outline;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: statusColor.withOpacity(0.1),
                  child: Icon(statusIcon, color: statusColor),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user['name'] as String,
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      Text(
                        user['phone'] as String,
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    status.toUpperCase(),
                    style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (address != null) ...[
              Row(
                children: [
                  Icon(Icons.location_on, size: 16, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      address,
                      style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text(
                  'Last update: ${_formatDateTime(lastUpdated)}',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
                const Spacer(),
                if (batteryLevel != null) ...[
                  Icon(
                    batteryLevel > 20 ? Icons.battery_std : Icons.battery_alert,
                    size: 16,
                    color: batteryLevel > 20 ? Colors.green : Colors.red,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '$batteryLevel%',
                    style: TextStyle(
                      fontSize: 12,
                      color: batteryLevel > 20 ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showLocationDetails(location),
                    icon: const Icon(Icons.map, size: 16),
                    label: const Text('View on Map'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                if (status == 'needs_help')
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _sendHelp(location),
                      icon: const Icon(Icons.local_hospital, size: 16),
                      label: const Text('Send Help'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${dateTime.day}/${dateTime.month} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }

  void _showLocationDetails(Map<String, dynamic> location) {
    final latitude = location['latitude'] as double;
    final longitude = location['longitude'] as double;
    final accuracy = location['accuracy_meters'] as double?;
    final altitude = location['altitude'] as double?;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Location Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Latitude: $latitude'),
            Text('Longitude: $longitude'),
            if (accuracy != null) Text('Accuracy: ${accuracy.toStringAsFixed(1)}m'),
            if (altitude != null) Text('Altitude: ${altitude.toStringAsFixed(1)}m'),
            const SizedBox(height: 16),
            const Text('Note: Map integration will be implemented in the next phase.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _sendHelp(Map<String, dynamic> location) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Send Help'),
        content: const Text('Emergency response dispatch will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Help dispatch functionality will be implemented')),
              );
            },
            child: const Text('Dispatch'),
          ),
        ],
      ),
    );
  }
}
