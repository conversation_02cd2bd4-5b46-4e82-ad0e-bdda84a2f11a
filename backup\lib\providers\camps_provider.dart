import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/camp.dart';
import '../services/camps_service.dart';

// Provider for all active camps
final campsProvider = FutureProvider<List<Camp>>((ref) async {
  return await CampsService.getActiveCamps();
});

// Provider for real-time camp updates
final campsStreamProvider = StreamProvider<List<Camp>>((ref) {
  return CampsService.subscribeToCampUpdates();
});

// Provider for camps by type
final campsByTypeProvider = FutureProvider.family<List<Camp>, String>((ref, type) async {
  return await CampsService.getCampsByType(type);
});

// Provider for nearby camps
final nearbyCampsProvider = FutureProvider.family<List<Camp>, Map<String, double>>((ref, params) async {
  final latitude = params['latitude']!;
  final longitude = params['longitude']!;
  final radius = params['radius'] ?? 10.0; // Default 10km radius

  return await CampsService.getNearbyCamps(latitude, longitude, radius);
});

// Provider for specific camp by ID
final campByIdProvider = FutureProvider.family<Camp?, String>((ref, campId) async {
  return await CampsService.getCampById(campId);
});

// Provider for real-time updates of a specific camp
final campStreamProvider = StreamProvider.family<Camp?, String>((ref, campId) {
  return CampsService.subscribeToCampById(campId);
});