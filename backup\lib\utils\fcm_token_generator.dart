import 'package:flutter/foundation.dart';

class FCMTokenGenerator {
  /// Generate a mock FCM token for testing purposes
  /// This simulates what a real FCM token would look like
  static String generateMockToken() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomPart = (timestamp % 1000000).toString().padLeft(6, '0');

    // This creates a realistic-looking FCM token for testing
    return 'fMockToken_AapatMitra_${randomPart}_eHxY7zQ:APA91bF${randomPart}MockTokenForTesting'
           'Emergency_Notifications_Delhi_${timestamp}_MockFCMToken';
  }

  /// Display token in console for easy copying
  static void displayTokenForTesting() {
    final token = generateMockToken();

    if (kDebugMode) {
      print('\n' + '=' * 80);
      print('🔥 FIREBASE FCM TOKEN FOR TESTING');
      print('=' * 80);
      print('Copy this token to Firebase Console:');
      print('');
      print(token);
      print('');
      print('Steps to test:');
      print('1. Copy the token above');
      print('2. Go to Firebase Console → Cloud Messaging');
      print('3. Click "Send your first message"');
      print('4. Paste token in "Test on device" section');
      print('5. Send emergency notification!');
      print('=' * 80);
      print('');
    }
  }

  /// Get token for web testing (if running on web)
  static String? getWebTestToken() {
    if (kIsWeb) {
      try {
        // For web testing, we'll use a consistent token
        return 'web_test_token_aapatmitra_emergency_${DateTime.now().day}${DateTime.now().month}';
      } catch (e) {
        print('Error generating web token: $e');
        return null;
      }
    }
    return null;
  }

  /// Copy token to clipboard (web only)
  static void copyToClipboard(String token) {
    if (kIsWeb) {
      try {
        // For web, we'll just display the token for manual copying
        print('Token ready for copying: $token');
        print('Please manually copy the token from console or UI');
      } catch (e) {
        print('Could not copy to clipboard: $e');
        print('Please manually copy the token from console');
      }
    }
  }
}
