import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'location_service.dart';
import 'alert_service.dart';
import 'alarm_service.dart';

class DisasterDetectionService {
  static final SupabaseClient _client = Supabase.instance.client;
  static Timer? _monitoringTimer;
  static bool _isMonitoring = false;
  static List<String> _activeDisasters = [];

  // API endpoints for disaster monitoring
  static const String _usgsEarthquakeAPI = 'https://earthquake.usgs.gov/earthquakes/feed/v1.0/summary/all_hour.geojson';
  static const String _weatherAPI = 'https://api.openweathermap.org/data/2.5/weather'; // Requires API key
  
  // Disaster thresholds
  static const double _earthquakeThreshold = 4.0; // Magnitude
  static const double _proximityThreshold = 100.0; // km radius
  
  /// Start automatic disaster monitoring
  static Future<void> startMonitoring() async {
    if (_isMonitoring) return;
    
    _isMonitoring = true;
    print('Starting disaster monitoring...');
    
    // Initial check
    await _checkForDisasters();
    
    // Set up periodic monitoring (every 15 seconds as per requirements)
    _monitoringTimer = Timer.periodic(
      const Duration(seconds: 15),
      (timer) async {
        if (_isMonitoring) {
          await _checkForDisasters();
        }
      },
    );
  }

  /// Stop disaster monitoring
  static Future<void> stopMonitoring() async {
    _isMonitoring = false;
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
    print('Stopped disaster monitoring');
  }

  /// Check for disasters from multiple sources
  static Future<void> _checkForDisasters() async {
    try {
      // Get current location for proximity checks
      final position = await LocationService.getCurrentLocation();
      if (position == null) return;

      // Check multiple sources
      await Future.wait([
        _checkUSGSEarthquakes(position.latitude, position.longitude),
        _checkWeatherAlerts(position.latitude, position.longitude),
        _checkGovernmentAlerts(position.latitude, position.longitude),
      ]);

    } catch (e) {
      print('Error checking for disasters: $e');
    }
  }

  /// Check USGS earthquake data
  static Future<void> _checkUSGSEarthquakes(double userLat, double userLng) async {
    try {
      final response = await http.get(Uri.parse(_usgsEarthquakeAPI));
      if (response.statusCode != 200) return;

      final data = json.decode(response.body);
      final features = data['features'] as List;

      for (var feature in features) {
        final properties = feature['properties'];
        final geometry = feature['geometry'];
        final coordinates = geometry['coordinates'] as List;

        final magnitude = properties['mag']?.toDouble() ?? 0.0;
        final longitude = coordinates[0]?.toDouble() ?? 0.0;
        final latitude = coordinates[1]?.toDouble() ?? 0.0;
        final place = properties['place'] ?? 'Unknown location';
        final time = properties['time'];

        // Check if earthquake meets threshold and is nearby
        if (magnitude >= _earthquakeThreshold) {
          final distance = LocationService.calculateDistance(
            userLat, userLng, latitude, longitude,
          ) / 1000; // Convert to km

          if (distance <= _proximityThreshold) {
            await _activateDisaster(
              type: 'earthquake',
              latitude: latitude,
              longitude: longitude,
              magnitude: magnitude,
              description: 'Earthquake detected: $place (Magnitude: $magnitude)',
              source: 'automatic',
              radiusKm: _calculateEarthquakeRadius(magnitude),
            );
          }
        }
      }
    } catch (e) {
      print('Error checking USGS earthquakes: $e');
    }
  }

  /// Check weather alerts (placeholder - requires API key)
  static Future<void> _checkWeatherAlerts(double userLat, double userLng) async {
    try {
      // TODO: Implement weather API integration
      // This would check for severe weather conditions like:
      // - Severe thunderstorms
      // - Tornadoes
      // - Flash floods
      // - Extreme temperatures
      
      // For now, we'll check our database for weather-related disasters
      await _checkDatabaseDisasters(userLat, userLng);
      
    } catch (e) {
      print('Error checking weather alerts: $e');
    }
  }

  /// Check government alerts from database
  static Future<void> _checkGovernmentAlerts(double userLat, double userLng) async {
    try {
      final response = await _client
          .from('government_alerts')
          .select('*')
          .eq('is_active', true)
          .gte('expires_at', DateTime.now().toIso8601String());

      for (var alert in response) {
        if (alert['severity'] == 'critical') {
          // Check if user is in target area
          final targetArea = alert['target_area'];
          if (targetArea != null) {
            // TODO: Implement polygon containment check
            // For now, activate for all critical alerts
            await AlertService.showCriticalAlert(
              title: alert['title'],
              message: alert['message'],
              alertType: alert['alert_type'],
            );
          }
        }
      }
    } catch (e) {
      print('Error checking government alerts: $e');
    }
  }

  /// Check database for existing disasters
  static Future<void> _checkDatabaseDisasters(double userLat, double userLng) async {
    try {
      final response = await _client
          .from('disasters')
          .select('*')
          .eq('is_active', true);

      for (var disaster in response) {
        final disasterId = disaster['id'];
        
        if (!_activeDisasters.contains(disasterId)) {
          _activeDisasters.add(disasterId);
          
          // Activate emergency protocols
          await _activateEmergencyProtocols(disaster);
        }
      }
    } catch (e) {
      print('Error checking database disasters: $e');
    }
  }

  /// Activate disaster in database
  static Future<void> _activateDisaster({
    required String type,
    required double latitude,
    required double longitude,
    required double magnitude,
    required String description,
    required String source,
    required int radiusKm,
  }) async {
    try {
      // Check if similar disaster already exists
      final existing = await _client
          .from('disasters')
          .select('id')
          .eq('type', type)
          .eq('is_active', true)
          .limit(1);

      if (existing.isNotEmpty) return; // Disaster already active

      // Create new disaster record
      final response = await _client
          .from('disasters')
          .insert({
            'type': type,
            'location': 'POINT($longitude $latitude)',
            'magnitude': magnitude,
            'radius_km': radiusKm,
            'is_active': true,
            'activation_source': source,
            'description': description,
          })
          .select()
          .single();

      final disasterId = response['id'];
      _activeDisasters.add(disasterId);

      // Activate emergency protocols
      await _activateEmergencyProtocols(response);

      print('Disaster activated: $type at ($latitude, $longitude)');
      
    } catch (e) {
      print('Error activating disaster: $e');
    }
  }

  /// Activate emergency protocols
  static Future<void> _activateEmergencyProtocols(Map<String, dynamic> disaster) async {
    try {
      // 1. Start persistent alarm
      await AlarmService.startEmergencyAlarm();

      // 2. Start emergency location tracking
      await LocationService.startLocationTracking(isEmergency: true);

      // 3. Send emergency alert
      await AlertService.sendEmergencyAlert(
        title: 'EMERGENCY: ${disaster['type'].toString().toUpperCase()}',
        message: disaster['description'] ?? 'Emergency situation detected in your area.',
        disasterType: disaster['type'],
      );

      // 4. Save emergency state
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('emergency_active', true);
      await prefs.setString('active_disaster_id', disaster['id']);
      await prefs.setString('disaster_type', disaster['type']);

      print('Emergency protocols activated for ${disaster['type']}');
      
    } catch (e) {
      print('Error activating emergency protocols: $e');
    }
  }

  /// Deactivate disaster
  static Future<void> deactivateDisaster(String disasterId) async {
    try {
      await _client
          .from('disasters')
          .update({'is_active': false})
          .eq('id', disasterId);

      _activeDisasters.remove(disasterId);

      // Check if this was the last active disaster
      final activeDisasters = await _client
          .from('disasters')
          .select('id')
          .eq('is_active', true);

      if (activeDisasters.isEmpty) {
        await _deactivateEmergencyProtocols();
      }

    } catch (e) {
      print('Error deactivating disaster: $e');
    }
  }

  /// Deactivate emergency protocols
  static Future<void> _deactivateEmergencyProtocols() async {
    try {
      // Stop alarm (only if user manually stops it)
      // await AlarmService.stopAlarm();

      // Stop emergency location tracking
      await LocationService.stopLocationTracking();

      // Clear emergency state
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('emergency_active', false);
      await prefs.remove('active_disaster_id');
      await prefs.remove('disaster_type');

      print('Emergency protocols deactivated');
      
    } catch (e) {
      print('Error deactivating emergency protocols: $e');
    }
  }

  /// Calculate earthquake affected radius based on magnitude
  static int _calculateEarthquakeRadius(double magnitude) {
    // Rough calculation: radius increases exponentially with magnitude
    if (magnitude >= 7.0) return 500; // 500km for major earthquakes
    if (magnitude >= 6.0) return 200; // 200km for strong earthquakes
    if (magnitude >= 5.0) return 100; // 100km for moderate earthquakes
    return 50; // 50km for light earthquakes
  }

  /// Manual disaster activation (for government users)
  static Future<bool> manuallyActivateDisaster({
    required String type,
    required double latitude,
    required double longitude,
    required String description,
    double? magnitude,
    int? radiusKm,
  }) async {
    try {
      await _activateDisaster(
        type: type,
        latitude: latitude,
        longitude: longitude,
        magnitude: magnitude ?? 0.0,
        description: description,
        source: 'government',
        radiusKm: radiusKm ?? 50,
      );
      return true;
    } catch (e) {
      print('Error manually activating disaster: $e');
      return false;
    }
  }

  /// Check if emergency is currently active
  static Future<bool> isEmergencyActive() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('emergency_active') ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Get active disaster info
  static Future<Map<String, dynamic>?> getActiveDisasterInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final disasterId = prefs.getString('active_disaster_id');
      
      if (disasterId != null) {
        final response = await _client
            .from('disasters')
            .select('*')
            .eq('id', disasterId)
            .eq('is_active', true)
            .single();
        
        return response;
      }
    } catch (e) {
      print('Error getting active disaster info: $e');
    }
    return null;
  }

  // Getters
  static bool get isMonitoring => _isMonitoring;
  static List<String> get activeDisasters => _activeDisasters;
}
