class User {
  final String id;
  final String name;
  final String phone;
  final String? email;
  final String? address;
  final double? lastLatitude;
  final double? lastLongitude;
  final String? status;
  final DateTime? lastLocationUpdate;
  final bool isEmergencyTracking;

  User({
    required this.id,
    required this.name,
    required this.phone,
    this.email,
    this.address,
    this.lastLatitude,
    this.lastLongitude,
    this.status,
    this.lastLocationUpdate,
    this.isEmergencyTracking = false,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      phone: json['phone'],
      email: json['email'],
      address: json['address'],
      lastLatitude: json['last_latitude']?.toDouble(),
      lastLongitude: json['last_longitude']?.toDouble(),
      status: json['status'],
      lastLocationUpdate: json['last_location_update'] != null
          ? DateTime.parse(json['last_location_update'])
          : null,
      isEmergencyTracking: json['is_emergency_tracking'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'address': address,
      'last_latitude': lastLatitude,
      'last_longitude': lastLongitude,
      'status': status,
      'last_location_update': lastLocationUpdate?.toIso8601String(),
      'is_emergency_tracking': isEmergencyTracking,
    };
  }

  // Mock user
  static User mock() => User(
    id: '1',
    name: 'John Doe',
    phone: '+91 9876543210',
    status: 'safe',
    isEmergencyTracking: false,
  );
} 