class User {
  final String id;
  final String name;
  final String phone;
  final String status; // 'safe', 'unsafe', 'needs_help'
  final List<String> emergencyContacts;

  User({
    required this.id,
    required this.name,
    required this.phone,
    required this.status,
    required this.emergencyContacts,
  });

  // Mock user
  static User mock() => User(
    id: '1',
    name: '<PERSON>',
    phone: '+91 9876543210',
    status: 'safe',
    emergencyContacts: ['+91 9123456789'],
  );
} 