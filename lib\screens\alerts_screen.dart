import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/alert_service.dart';
import '../models/government_alert.dart';

// Simple provider for alerts
final alertsProvider = FutureProvider<List<Map<String, dynamic>>>((ref) async {
  return await AlertService.getActiveAlerts();
});

class AlertsScreen extends ConsumerWidget {
  const AlertsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final alertsAsync = ref.watch(alertsProvider);
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Alerts'),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                child: Text('Government Alerts',
                    style: textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: alertsAsync.when(
                  data: (alerts) {
                    if (alerts.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.notifications_none, size: 64, color: Colors.grey.shade400),
                            const SizedBox(height: 16),
                            Text('No active alerts', style: textTheme.titleMedium?.copyWith(color: Colors.grey.shade600)),
                            const SizedBox(height: 8),
                            Text('You will be notified when emergency alerts are issued', 
                                style: textTheme.bodyMedium?.copyWith(color: Colors.grey.shade500)),
                          ],
                        ),
                      );
                    }
                    
                    return ListView.separated(
                      itemCount: alerts.length,
                      separatorBuilder: (_, __) => const SizedBox(height: 8),
                      itemBuilder: (context, i) {
                        final alert = alerts[i];
                        Color cardColor;
                        IconData icon;
                        final severity = alert['severity'] as String? ?? 'info';
                        switch (severity) {
                          case 'urgent':
                          case 'critical':
                            cardColor = Colors.red.shade100;
                            icon = Icons.warning_amber_rounded;
                            break;
                          case 'warning':
                            cardColor = Colors.orange.shade100;
                            icon = Icons.warning;
                            break;
                          case 'info':
                          default:
                            cardColor = Theme.of(context).cardTheme.color ?? Colors.white;
                            icon = Icons.info_outline;
                        }
                        return Card(
                          color: cardColor,
                          child: ListTile(
                            leading: Icon(icon, color: Colors.brown.shade700, size: 28),
                            title: Text(alert['title'] ?? 'Alert', 
                                style: textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                            subtitle: Text(alert['message'] ?? 'No details available', 
                                style: textTheme.bodyMedium),
                            onTap: () {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Alert: ${alert['title']}'),
                                  duration: const Duration(seconds: 2),
                                ),
                              );
                            },
                            contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                          ),
                        );
                      },
                    );
                  },
                  loading: () => const Center(child: CircularProgressIndicator()),
                  error: (error, stack) => Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, size: 64, color: Colors.red.shade400),
                        const SizedBox(height: 16),
                        Text('Error loading alerts', style: textTheme.titleMedium),
                        const SizedBox(height: 8),
                        Text('Please check your connection and try again', 
                            style: textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600)),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => ref.refresh(alertsProvider),
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
