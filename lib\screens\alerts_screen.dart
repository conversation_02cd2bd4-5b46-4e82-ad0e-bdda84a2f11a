import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/alert_service.dart';

class AlertsScreen extends ConsumerWidget {
  const AlertsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final alerts = ref.watch(alertsProvider);
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                child: Text('Government Alerts',
                    style: textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: ListView.separated(
                  itemCount: alerts.length,
                  separatorBuilder: (_, __) => const SizedBox(height: 8),
                  itemBuilder: (context, i) {
                    final alert = alerts[i];
                    Color cardColor;
                    IconData icon;
                    switch (alert.severity) {
                      case 'urgent':
                      case 'critical':
                        cardColor = Colors.red.shade100;
                        icon = Icons.warning_amber_rounded;
                        break;
                      case 'warning':
                        cardColor = Colors.orange.shade100;
                        icon = Icons.warning;
                        break;
                      case 'info':
                      default:
                        cardColor = Theme.of(context).cardTheme.color ?? Colors.white;
                        icon = Icons.info_outline;
                    }
                    return Card(
                      color: cardColor,
                      child: ListTile(
                        leading: Icon(icon, color: Colors.brown.shade700, size: 28),
                        title: Text(alert.title, style: textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                        subtitle: Text(alert.message, style: textTheme.bodyMedium),
                        onTap: () {
                           ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('Tapped on alert: [alert.title]'),
                              duration: Duration(seconds: 1),
                            ),
                          );
                        },
                        contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}