-- URGENT: Execute this SQL in Supabase to fix infinite recursion
-- FINAL CORRECTED VERSION - Fixed the JSONB issue

-- 1. Temporarily disable <PERSON><PERSON> to fix the policies
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.government_users DISABLE ROW LEVEL SECURITY;

-- 2. Drop all problematic policies
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.users;
DROP POLICY IF EXISTS "Government users can view all users during disasters" ON public.users;
DROP POLICY IF EXISTS "Government users can view all users" ON public.users;

-- 3. Create simple, non-recursive policies
CREATE POLICY "users_select_own" ON public.users
  FOR SELECT USING (auth.uid() = auth_user_id);

CREATE POLICY "users_insert_own" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = auth_user_id);

CREATE POLICY "users_update_own" ON public.users
  FOR UPDATE USING (auth.uid() = auth_user_id);

-- 4. Simple government users policy
CREATE POLICY "government_users_select" ON public.government_users
  FOR ALL USING (true); -- Temporarily allow all access

-- 5. Re-enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.government_users ENABLE ROW LEVEL SECURITY;

-- 6. Create a test government user for current session
-- First check if user exists, if not insert
DO $$
BEGIN
  -- Insert user if not exists
  IF NOT EXISTS (SELECT 1 FROM public.users WHERE auth_user_id = auth.uid()) THEN
    INSERT INTO public.users (auth_user_id, phone, name, emergency_contacts)
    VALUES (
      auth.uid(),
      '+91-9876543210',
      'Test Admin User',
      '["91-9123456789", "+91-9876543211"]'::jsonb
    );
  END IF;
  
  -- Insert government user if not exists
  IF NOT EXISTS (
    SELECT 1 FROM public.government_users gu 
    JOIN public.users u ON gu.user_id = u.id 
    WHERE u.auth_user_id = auth.uid()
  ) THEN
    INSERT INTO public.government_users (user_id, department, clearance_level, region_access, can_activate_disasters)
    SELECT 
      u.id,
      'admin',
      5,
      ARRAY['Delhi', 'NCR', 'All'],
      true
    FROM public.users u 
    WHERE u.auth_user_id = auth.uid();
  END IF;
END $$;

-- Add new fields to government_users table
ALTER TABLE public.government_users
ADD COLUMN IF NOT EXISTS can_manage_camps BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS can_manage_alerts BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS can_view_all_users BOOLEAN DEFAULT false;

-- Add new fields to disasters table
ALTER TABLE public.disasters
ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES public.government_users(id),
ADD COLUMN IF NOT EXISTS updated_by UUID REFERENCES public.government_users(id);

-- Add new fields to rescue_camps table
ALTER TABLE public.rescue_camps
ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES public.government_users(id),
ADD COLUMN IF NOT EXISTS updated_by UUID REFERENCES public.government_users(id);

-- Add new fields to government_alerts table
ALTER TABLE public.government_alerts
ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES public.government_users(id),
ADD COLUMN IF NOT EXISTS updated_by UUID REFERENCES public.government_users(id);

-- Create disaster_events table for tracking disaster history
CREATE TABLE IF NOT EXISTS public.disaster_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type VARCHAR NOT NULL,
  description TEXT NOT NULL,
  latitude DECIMAL(10,8) NOT NULL,
  longitude DECIMAL(11,8) NOT NULL,
  magnitude DECIMAL,
  radius_km INTEGER,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES public.government_users(id),
  updated_by UUID REFERENCES public.government_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create alerts table for general alerts
CREATE TABLE IF NOT EXISTS public.alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR NOT NULL,
  message TEXT NOT NULL,
  severity VARCHAR CHECK (severity IN ('info', 'warning', 'urgent', 'critical')) DEFAULT 'info',
  target_areas TEXT[] DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES public.government_users(id),
  updated_by UUID REFERENCES public.government_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP
);

-- Create user_statuses table for tracking user safety status
CREATE TABLE IF NOT EXISTS public.user_statuses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  status VARCHAR CHECK (status IN ('safe', 'unsafe', 'needs_help', 'no_response')) DEFAULT 'no_response',
  last_latitude DECIMAL(10,8),
  last_longitude DECIMAL(11,8),
  last_updated TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create user_locations table for location history
CREATE TABLE IF NOT EXISTS public.user_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  latitude DECIMAL(10,8) NOT NULL,
  longitude DECIMAL(11,8) NOT NULL,
  timestamp TIMESTAMP DEFAULT NOW(),
  accuracy_meters DECIMAL,
  altitude DECIMAL,
  address TEXT
);

-- Enable RLS on new tables
ALTER TABLE public.disaster_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.alerts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_statuses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_locations ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for new tables
CREATE POLICY "Everyone can view active disaster events" ON public.disaster_events
  FOR SELECT USING (is_active = true);

CREATE POLICY "Government users can manage disaster events" ON public.disaster_events
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      JOIN public.users u ON gu.user_id = u.id 
      WHERE u.auth_user_id = auth.uid() AND gu.can_activate_disasters = true
    )
  );

CREATE POLICY "Everyone can view active alerts" ON public.alerts
  FOR SELECT USING (is_active = true);

CREATE POLICY "Government users can manage alerts" ON public.alerts
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      JOIN public.users u ON gu.user_id = u.id 
      WHERE u.auth_user_id = auth.uid() AND gu.can_manage_alerts = true
    )
  );

CREATE POLICY "Users can view their own status" ON public.user_statuses
  FOR SELECT USING (user_id IN (SELECT id FROM public.users WHERE auth_user_id = auth.uid()));

CREATE POLICY "Government users can view all user statuses" ON public.user_statuses
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      JOIN public.users u ON gu.user_id = u.id 
      WHERE u.auth_user_id = auth.uid() AND gu.can_view_all_users = true
    )
  );

CREATE POLICY "Users can manage their own location history" ON public.user_locations
  FOR ALL USING (user_id IN (SELECT id FROM public.users WHERE auth_user_id = auth.uid()));

CREATE POLICY "Government users can view all location history" ON public.user_locations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      JOIN public.users u ON gu.user_id = u.id 
      WHERE u.auth_user_id = auth.uid() AND gu.can_view_all_users = true
    )
  );

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_disaster_events_active ON public.disaster_events(is_active);
CREATE INDEX IF NOT EXISTS idx_alerts_active ON public.alerts(is_active);
CREATE INDEX IF NOT EXISTS idx_user_statuses_user ON public.user_statuses(user_id);
CREATE INDEX IF NOT EXISTS idx_user_locations_user_time ON public.user_locations(user_id, timestamp);

-- Add triggers for automatic timestamp updates
CREATE TRIGGER update_disaster_events_updated_at 
  BEFORE UPDATE ON public.disaster_events
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alerts_updated_at 
  BEFORE UPDATE ON public.alerts
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_statuses_updated_at 
  BEFORE UPDATE ON public.user_statuses
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
