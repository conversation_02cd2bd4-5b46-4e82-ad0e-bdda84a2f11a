import 'package:flutter/material.dart';
import '../models/camp.dart';

class CampDetailsScreen extends StatelessWidget {
  final Camp camp;
  const CampDetailsScreen({super.key, required this.camp});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text('Camp Details'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              elevation: 4, // Slightly more emphasis for the main card
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.cabin, color: Theme.of(context).colorScheme.primary, size: 36),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            camp.name,
                            style: textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
                          ),
                        ),
                        Chip(
                          label: Text('Open', style: textTheme.labelSmall?.copyWith(color: Colors.white, fontWeight: FontWeight.bold)),
                          backgroundColor: Colors.green,
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        const Icon(Icons.people, color: Colors.brown),
                        const SizedBox(width: 8),
                        Text('Capacity: ', style: textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold)),
                        Text('${camp.occupancy}/${camp.capacity}', style: textTheme.titleSmall),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        const Icon(Icons.fastfood, color: Colors.brown),
                        const SizedBox(width: 8),
                        Text('Food: ', style: textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold)),
                        Text(camp.foodStatus, style: textTheme.titleSmall),
                        const SizedBox(width: 16),
                        const Icon(Icons.water_drop, color: Colors.brown),
                        const SizedBox(width: 8),
                        Text('Water: ', style: textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold)),
                        Text(camp.waterStatus, style: textTheme.titleSmall),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text('Address', style: textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Text('123 Main St, Mumbai', style: textTheme.bodyLarge), // Example address
            const SizedBox(height: 18),
            Text('Contact', style: textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.phone, color: Colors.brown),
                const SizedBox(width: 8),
                Text('+91 9876543210', style: textTheme.bodyLarge), // Example contact
              ],
            ),
            const Spacer(),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Get Directions: Feature not implemented yet.'),
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                child: const Text('Get Directions'),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 