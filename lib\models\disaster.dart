class Disaster {
  final String id;
  final String type;
  final String description;
  final double latitude;
  final double longitude;
  final double magnitude;
  final double radiusKm;
  final bool isActive;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Disaster({
    required this.id,
    required this.type,
    required this.description,
    required this.latitude,
    required this.longitude,
    required this.magnitude,
    required this.radiusKm,
    required this.isActive,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
  });

  factory Disaster.fromJson(Map<String, dynamic> json) {
    return Disaster(
      id: json['id'],
      type: json['type'],
      description: json['description'],
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      magnitude: json['magnitude'].toDouble(),
      radiusKm: json['radius_km'].toDouble(),
      isActive: json['is_active'],
      createdBy: json['created_by'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'magnitude': magnitude,
      'radius_km': radiusKm,
      'is_active': isActive,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  String get severityLevel {
    switch (type) {
      case 'earthquake':
        if (magnitude >= 7.0) return 'Extreme';
        if (magnitude >= 6.0) return 'Severe';
        if (magnitude >= 5.0) return 'Moderate';
        return 'Minor';
      case 'flood':
        if (magnitude >= 10.0) return 'Extreme';
        if (magnitude >= 7.0) return 'Severe';
        if (magnitude >= 4.0) return 'Moderate';
        return 'Minor';
      case 'cyclone':
        if (magnitude >= 200.0) return 'Extreme';
        if (magnitude >= 150.0) return 'Severe';
        if (magnitude >= 100.0) return 'Moderate';
        return 'Minor';
      default:
        return 'Unknown';
    }
  }

  String get magnitudeUnit {
    switch (type) {
      case 'earthquake':
        return 'Richter';
      case 'flood':
        return 'm';
      case 'cyclone':
        return 'km/h';
      default:
        return '';
    }
  }

  String get displayMagnitude {
    return '$magnitude $magnitudeUnit';
  }

  // Mock disaster for testing
  static Disaster mockDisaster = Disaster(
    id: '1',
    type: 'earthquake',
    description: 'Strong earthquake in Delhi NCR',
    latitude: 28.6139,
    longitude: 77.2090,
    magnitude: 6.5,
    radiusKm: 50.0,
    isActive: true,
    createdBy: 'system',
    createdAt: DateTime.now(),
  );
} 