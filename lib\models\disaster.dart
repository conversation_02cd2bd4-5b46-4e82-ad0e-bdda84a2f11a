class Disaster {
  final String id;
  final String type; // 'earthquake', 'flood', etc.
  final String location;
  final double magnitude;
  final bool isActive;

  Disaster({
    required this.id,
    required this.type,
    required this.location,
    required this.magnitude,
    required this.isActive,
  });

  // Mock disaster
  static Disaster mock() => Disaster(
    id: '1',
    type: 'earthquake',
    location: 'Zone A',
    magnitude: 6.2,
    isActive: true,
  );
} 