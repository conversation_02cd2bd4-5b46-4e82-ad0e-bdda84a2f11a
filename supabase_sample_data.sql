-- Sample Data for AapatMitra Testing
-- Execute these commands after setting up the schema and RLS policies

-- Insert sample rescue camps
INSERT INTO public.rescue_camps (name, location, address, type, capacity, current_occupancy, facilities, contact_info, resource_levels) VALUES
('Central Evacuation Center', POINT(77.2090, 28.6139), 'Connaught Place, New Delhi', 'evacuation_center', 500, 120, '["food", "water", "medical", "shelter", "power", "wifi"]', '{"phone": "+91-11-23456789", "email": "<EMAIL>"}', '{"food": 85, "water": 70, "medical": 90, "shelter": 76}'),
('Medical Relief Camp Alpha', POINT(77.2167, 28.6333), 'India Gate, New Delhi', 'medical_camp', 200, 45, '["medical", "water", "power"]', '{"phone": "+91-11-23456790", "email": "<EMAIL>"}', '{"medical": 95, "water": 80}'),
('Food Distribution Center', POINT(77.1910, 28.5355), 'Qutub Minar, New Delhi', 'food_distribution', 300, 180, '["food", "water"]', '{"phone": "+91-11-23456791"}', '{"food": 60, "water": 85}'),
('Emergency Shelter Beta', POINT(77.2411, 28.6129), 'Red Fort, New Delhi', 'shelter', 400, 320, '["shelter", "food", "water", "power"]', '{"phone": "+91-11-23456792"}', '{"food": 40, "water": 55, "shelter": 80}');

-- Insert sample hazard zones
INSERT INTO public.hazard_zones (location, hazard_type, severity_level, description, safety_instructions, is_active, evacuation_required) VALUES
(ST_GeomFromText('POLYGON((77.200 28.610, 77.210 28.610, 77.210 28.620, 77.200 28.620, 77.200 28.610))')::POLYGON, 'flood', 4, 'Severe flooding near Yamuna River', 'Avoid this area. Use alternative routes. Do not attempt to cross flooded roads.', true, true),
(ST_GeomFromText('POLYGON((77.180 28.630, 77.190 28.630, 77.190 28.640, 77.180 28.640, 77.180 28.630))')::POLYGON, 'building_collapse', 5, 'Building collapse risk in old city area', 'Immediate evacuation required. Do not enter buildings in this zone.', true, true),
(ST_GeomFromText('POLYGON((77.220 28.600, 77.230 28.600, 77.230 28.610, 77.220 28.610, 77.220 28.600))')::POLYGON, 'gas_leak', 3, 'Gas pipeline leak detected', 'Avoid open flames. Evacuate if you smell gas. Call emergency services.', true, false),
(ST_GeomFromText('POLYGON((77.160 28.650, 77.170 28.650, 77.170 28.660, 77.160 28.660, 77.160 28.650))')::POLYGON, 'fire', 2, 'Small fire contained but smoke hazard', 'Avoid area due to smoke. Use masks if necessary.', false, false);

-- Insert sample safe routes
INSERT INTO public.safe_routes (route_name, start_point, end_point, waypoints, route_status, estimated_travel_time, transportation_mode, capacity_limit, current_usage) VALUES
('Route A - Central to North', POINT(77.2090, 28.6139), POINT(77.2167, 28.6333), '[{"lat": 77.2120, "lng": 28.6200}, {"lat": 77.2150, "lng": 28.6280}]', 'safe', '00:15:00', 'walking', 200, 45),
('Route B - East Bypass', POINT(77.2411, 28.6129), POINT(77.1910, 28.5355), '[{"lat": 77.2300, "lng": 28.6000}, {"lat": 77.2100, "lng": 28.5700}]', 'caution', '00:25:00', 'walking', 150, 120),
('Route C - Emergency Vehicle Path', POINT(77.1910, 28.5355), POINT(77.2167, 28.6333), '[{"lat": 77.2000, "lng": 28.5800}, {"lat": 77.2100, "lng": 28.6100}]', 'safe', '00:12:00', 'vehicle', 50, 15),
('Route D - Blocked Area', POINT(77.200, 28.615), POINT(77.210, 28.625), '[]', 'blocked', null, 'walking', 0, 0);

-- Insert sample emergency facilities
INSERT INTO public.emergency_facilities (name, type, location, address, contact_info, services_available, current_capacity, operational_status) VALUES
('All India Institute of Medical Sciences', 'hospital', POINT(77.2090, 28.5672), 'Ansari Nagar, New Delhi', '{"phone": "+91-11-26588500", "emergency": "+91-11-26588700"}', '["emergency_room", "surgery", "icu", "trauma_center", "burn_unit"]', 85, 'fully_operational'),
('Delhi Fire Station Central', 'fire_station', POINT(77.2167, 28.6289), 'Connaught Place Fire Station', '{"phone": "+91-11-23456800", "emergency": "101"}', '["fire_rescue", "emergency_response", "ambulance"]', 95, 'fully_operational'),
('Parliament Street Police Station', 'police_station', POINT(77.2194, 28.6239), 'Parliament Street, New Delhi', '{"phone": "+91-11-23456801", "emergency": "100"}', '["emergency_response", "traffic_control", "crowd_management"]', 90, 'fully_operational'),
('Apollo Pharmacy Emergency', 'pharmacy', POINT(77.2250, 28.6350), 'Khan Market, New Delhi', '{"phone": "+91-11-23456802"}', '["emergency_medicines", "first_aid", "medical_supplies"]', 70, 'limited_capacity');

-- Insert sample government alerts
INSERT INTO public.government_alerts (title, message, alert_type, severity, target_area, expires_at) VALUES
('Evacuation Notice - Zone A', 'Immediate evacuation required for Zone A due to severe flooding. Proceed to Central Evacuation Center.', 'evacuation', 'critical', ST_GeomFromText('POLYGON((77.200 28.610, 77.210 28.610, 77.210 28.620, 77.200 28.620, 77.200 28.610))')::POLYGON, NOW() + INTERVAL '6 hours'),
('Shelter Capacity Update', 'Emergency Shelter Beta is nearing full capacity. Alternative shelters available at Central Evacuation Center.', 'shelter_info', 'warning', null, NOW() + INTERVAL '2 hours'),
('Medical Supplies Available', 'Medical Relief Camp Alpha has additional medical supplies available for emergency treatment.', 'resource_update', 'info', null, NOW() + INTERVAL '12 hours'),
('Road Closure Alert', 'Route D is temporarily blocked due to debris. Use alternative Route A or Route C.', 'safety_warning', 'warning', null, NOW() + INTERVAL '4 hours');

-- Insert a sample active disaster
INSERT INTO public.disasters (type, location, magnitude, radius_km, is_active, activation_source, description) VALUES
('earthquake', POINT(77.2090, 28.6139), 6.2, 50, true, 'automatic', 'Major earthquake detected in Delhi NCR region. Magnitude 6.2 with epicenter near Central Delhi.');

-- Note: To insert actual users and government users, you'll need to:
-- 1. Create users through the authentication system first
-- 2. Then insert corresponding records in the users and government_users tables
-- 3. The auth_user_id should match the UUID from auth.users table

-- Example of how to insert a user after authentication (replace with actual auth UUID):
-- INSERT INTO public.users (auth_user_id, phone, name, emergency_contacts) VALUES
-- ('your-auth-uuid-here', '+91-9876543210', 'John Doe', '["+91-9123456789", "+91-9876543211"]');

-- Example of how to create a government user (after creating the user above):
-- INSERT INTO public.government_users (user_id, department, clearance_level, can_activate_disasters) VALUES
-- ((SELECT id FROM public.users WHERE phone = '+91-9876543210'), 'admin', 5, true);
