import 'dart:async';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocationService {
  static final SupabaseClient _client = Supabase.instance.client;
  static StreamSubscription<Position>? _positionStream;
  static Position? _lastKnownPosition;
  static String? _lastKnownAddress;
  static bool _isTracking = false;
  static Timer? _locationUpdateTimer;
  static bool _isEmergencyTracking = false;

  // Location tracking settings
  static const LocationSettings _locationSettings = LocationSettings(
    accuracy: LocationAccuracy.high,
    distanceFilter: 100,
  );

  static const LocationSettings _emergencyLocationSettings = LocationSettings(
    accuracy: LocationAccuracy.best,
    distanceFilter: 10,
  );

  static Future<void> initialize() async {
    await _checkLocationPermission();
  }

  /// Check and request location permissions
  static Future<bool> _checkLocationPermission() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return false;
      }

      // Check current permission status
      LocationPermission permission = await Geolocator.checkPermission();
      
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          return false;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        // Open app settings for user to manually enable
        await openAppSettings();
        return false;
      }

      // For background location (required for emergency tracking)
      if (permission == LocationPermission.whileInUse) {
        permission = await Geolocator.requestPermission();
      }

      return permission == LocationPermission.always || 
             permission == LocationPermission.whileInUse;
    } catch (e) {
      print('Error requesting location permission: $e');
      return false;
    }
  }

  /// Get current location
  static Future<Position?> getCurrentLocation() async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      _lastKnownPosition = position;
      await _updateAddressFromPosition(position);
      return position;
    } catch (e) {
      print('Error getting current location: $e');
      return _lastKnownPosition;
    }
  }

  /// Start continuous location tracking
  static Future<bool> startLocationTracking() async {
    try {
      if (_positionStream != null) return true;

      final hasPermission = await _checkLocationPermission();
      if (!hasPermission) return false;

      _isTracking = true;
      
      _positionStream = Geolocator.getPositionStream(
        locationSettings: _locationSettings,
      ).listen(
        (Position position) async {
          _lastKnownPosition = position;
          await _updateAddressFromPosition(position);
          await _saveLocationToDatabase(position, false);
        },
        onError: (error) {
          print('Location tracking error: $error');
        },
      );

      // Also set up periodic updates for redundancy
      _locationUpdateTimer = Timer.periodic(
        Duration(seconds: 30),
        (timer) async {
          if (_isTracking) {
            Position? position = await getCurrentLocation();
            if (position != null) {
              await _saveLocationToDatabase(position, false);
            }
          }
        },
      );

      return true;
    } catch (e) {
      print('Error starting location tracking: $e');
      return false;
    }
  }

  /// Start emergency location tracking
  static Future<bool> startEmergencyTracking() async {
    try {
      if (_isEmergencyTracking) return true;
      _isEmergencyTracking = true;

      final hasPermission = await _checkLocationPermission();
      if (!hasPermission) return false;

      if (_positionStream != null) {
        await _positionStream?.cancel();
      }

      _positionStream = Geolocator.getPositionStream(
        locationSettings: _emergencyLocationSettings,
      ).listen(
        (Position position) async {
          _lastKnownPosition = position;
          await _updateAddressFromPosition(position);
          await _saveLocationToDatabase(position, true);
        },
        onError: (error) {
          print('Emergency location tracking error: $error');
        },
      );

      // Initial location update
      Position? position = await getCurrentLocation();
      if (position != null) {
        await _saveLocationToDatabase(position, true);
      }

      return true;
    } catch (e) {
      print('Error starting emergency location tracking: $e');
      return false;
    }
  }

  /// Stop location tracking
  static Future<void> stopLocationTracking() async {
    _isTracking = false;
    await _positionStream?.cancel();
    _positionStream = null;
    _locationUpdateTimer?.cancel();
    _locationUpdateTimer = null;
  }

  /// Stop emergency location tracking
  static Future<void> stopEmergencyTracking() async {
    _isEmergencyTracking = false;
    await _positionStream?.cancel();
    _positionStream = null;
    await startLocationTracking(); // Resume normal tracking
  }

  /// Get address from coordinates
  static Future<String?> getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude);
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        return '${place.street}, ${place.subLocality}, ${place.locality}, ${place.administrativeArea}, ${place.postalCode}';
      }
      return null;
    } catch (e) {
      print('Error getting address: $e');
      return null;
    }
  }

  /// Update address from position
  static Future<void> _updateAddressFromPosition(Position position) async {
    _lastKnownAddress = await getAddressFromCoordinates(
      position.latitude, 
      position.longitude,
    );
  }

  /// Save location to Supabase database
  static Future<void> _saveLocationToDatabase(Position position, bool isEmergency) async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) return;

      // Get user ID from our users table
      final userResponse = await _client
          .from('users')
          .select('id')
          .eq('auth_user_id', user.id)
          .single();

      final userId = userResponse['id'];

      // Check if there's an active disaster
      final disasterResponse = await _client
          .from('disasters')
          .select('id')
          .eq('is_active', true)
          .limit(1);

      String? disasterId;
      if (disasterResponse.isNotEmpty) {
        disasterId = disasterResponse[0]['id'];
      }

      // Only save to live_user_locations if there's an active disaster or emergency
      if (disasterId != null || isEmergency) {
        await _client.from('live_user_locations').upsert({
          'user_id': userId,
          'disaster_id': disasterId,
          'latitude': position.latitude,
          'longitude': position.longitude,
          'altitude': position.altitude,
          'accuracy_meters': position.accuracy,
          'address': _lastKnownAddress,
          'status': isEmergency ? 'needs_help' : 'no_response',
          'last_updated': DateTime.now().toIso8601String(),
          'battery_level': await _getBatteryLevel(),
          'network_status': await _getNetworkStatus(),
        });
      }

      // Always save to local storage for offline access
      await _saveLocationLocally(position);
      
    } catch (e) {
      print('Error saving location to database: $e');
    }
  }

  /// Save location locally for offline access
  static Future<void> _saveLocationLocally(Position position) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble('last_latitude', position.latitude);
      await prefs.setDouble('last_longitude', position.longitude);
      await prefs.setString('last_address', _lastKnownAddress ?? '');
      await prefs.setString('last_location_time', DateTime.now().toIso8601String());
    } catch (e) {
      print('Error saving location locally: $e');
    }
  }

  /// Get last known location from local storage
  static Future<Position?> getLastKnownLocationFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final latitude = prefs.getDouble('last_latitude');
      final longitude = prefs.getDouble('last_longitude');
      
      if (latitude != null && longitude != null) {
        return Position(
          latitude: latitude,
          longitude: longitude,
          timestamp: DateTime.now(),
          accuracy: 0,
          altitude: 0,
          altitudeAccuracy: 0,
          heading: 0,
          headingAccuracy: 0,
          speed: 0,
          speedAccuracy: 0,
        );
      }
    } catch (e) {
      print('Error getting location from storage: $e');
    }
    return null;
  }

  /// Calculate distance between two points
  static double calculateDistance(
    double lat1, double lon1, 
    double lat2, double lon2,
  ) {
    return Geolocator.distanceBetween(lat1, lon1, lat2, lon2);
  }

  /// Get battery level (placeholder - would need battery_plus package)
  static Future<int> _getBatteryLevel() async {
    // TODO: Implement battery level detection
    return 100; // Placeholder
  }

  /// Get network status
  static Future<String> _getNetworkStatus() async {
    // TODO: Implement network status detection
    return 'wifi'; // Placeholder
  }

  // Getters
  static Position? get lastKnownPosition => _lastKnownPosition;
  static String? get lastKnownAddress => _lastKnownAddress;
  static bool get isTracking => _isTracking;
}
