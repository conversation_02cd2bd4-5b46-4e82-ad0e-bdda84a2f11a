# AI Prompt for AapatMitra

## Project Overview
Create a comprehensive disaster response mobile application using Flutter and Supabase that helps locate and rescue people during natural disasters like earthquakes. The app combines pre-disaster alerts with real-time location sharing for emergency response.

## Core App Requirements

### 1. Automatic Disaster Detection & Government Integration
- **Automatic Detection**: Integrate with multiple disaster APIs (USGS Earthquake API, weather services, seismic networks)
- **Government Dashboard**: Separate government/emergency services interface with administrative controls
- **Auto-Activation**: System automatically activates when disaster threshold is met (no manual intervention needed)
- **Persistent Alarms**: Continuous alarm ringing that CANNOT be disabled until user manually stops it
- **Government Override**: Government can manually activate the system for any area/region
- **Multi-Source Validation**: Cross-reference multiple data sources before activation

### 2. Precise Location Sharing & Safety Status
- **WhatsApp-Style Location**: Exact pinpoint location with street-level accuracy
- **Live Location Sharing**: Continuous real-time GPS tracking during disasters (like WhatsApp live location)
- **Automatic Location Broadcasting**: When disaster activates, ALL users automatically share location
- **Government Location Access**: Government dashboard shows EVERY user's precise location in affected area
- **No Location Opt-Out**: During active disasters, location sharing is mandatory for safety
- **Address Details**: Show exact address, building name, floor number if available
- **Indoor Positioning**: Integrate with WiFi/Bluetooth beacons for indoor location accuracy

### 4. Government Resource & Safety Management System
- **Rescue Camp Management**: Government can add/update rescue camp locations with real-time capacity
- **Resource Tracking**: Food, water, medical supplies, shelter availability at each location
- **Hazard Zone Mapping**: Mark dangerous areas (flooding, building collapse, gas leaks, etc.)
- **Safe Route Planning**: Dynamic routing that avoids hazardous areas
- **Evacuation Center Information**: Capacity, facilities, contact information for each center
- **Real-time Updates**: Live updates on resource availability and safety conditions
- **Emergency Service Locations**: Hospitals, fire stations, police stations with current status

## Technical Stack Requirements

### Frontend (Flutter)
```yaml
dependencies:
  - flutter_riverpod: ^2.4.9 (state management)
  - supabase_flutter: ^2.0.0 (backend integration)
  - geolocator: ^10.1.0 (location services)
  - google_maps_flutter: ^2.5.0 (mapping)
  - firebase_messaging: ^14.7.9 (push notifications)
  - flutter_background_service: ^5.0.5 (background tasks)
  - audioplayers: ^5.2.1 (alarm sounds)
  - permission_handler: ^11.1.0 (device permissions)
  - connectivity_plus: ^5.0.2 (network status)
```

### Backend (Supabase)
- PostgreSQL database with real-time subscriptions
- Row Level Security (RLS) policies
- Edge Functions for custom logic
- File storage for emergency photos/videos
- Authentication with phone number verification

## Database Schema Design

```sql
-- Users table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone VARCHAR UNIQUE NOT NULL,
  name VARCHAR NOT NULL,
  emergency_contacts JSONB,
  building_info JSONB, -- floor, apartment, building name
  created_at TIMESTAMP DEFAULT NOW()
);

-- Disasters table
CREATE TABLE disasters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type VARCHAR NOT NULL, -- 'earthquake', 'flood', etc.
  location POINT NOT NULL,
  magnitude DECIMAL,
  radius_km INTEGER,
  is_active BOOLEAN DEFAULT true,
  activation_source VARCHAR, -- 'automatic', 'government', 'manual'
  government_activated BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Real-time user locations (WhatsApp-style)
CREATE TABLE live_user_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  disaster_id UUID REFERENCES disasters(id),
  latitude DECIMAL(10,8) NOT NULL, -- High precision for pinpoint accuracy
  longitude DECIMAL(11,8) NOT NULL,
  altitude DECIMAL,
  accuracy_meters DECIMAL,
  address TEXT, -- Reverse geocoded address
  building_floor INTEGER,
  indoor_location JSONB, -- WiFi/Bluetooth beacon data
  status VARCHAR CHECK (status IN ('safe', 'unsafe', 'no_response', 'needs_help')),
  alarm_status VARCHAR CHECK (alarm_status IN ('ringing', 'acknowledged', 'disabled_by_user')),
  last_updated TIMESTAMP DEFAULT NOW(),
  battery_level INTEGER,
  network_status VARCHAR
);

-- Government users and permissions
CREATE TABLE government_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  department VARCHAR NOT NULL, -- 'fire', 'police', 'medical', 'admin'
  clearance_level INTEGER, -- 1-5, higher = more access
  region_access TEXT[], -- Geographic areas they can monitor
  can_activate_disasters BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Rescue camps and evacuation centers
CREATE TABLE rescue_camps (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  location POINT NOT NULL,
  address TEXT NOT NULL,
  type VARCHAR NOT NULL, -- 'evacuation_center', 'medical_camp', 'food_distribution', 'shelter'
  capacity INTEGER NOT NULL,
  current_occupancy INTEGER DEFAULT 0,
  facilities JSONB, -- ['food', 'water', 'medical', 'shelter', 'power', 'wifi']
  contact_info JSONB,
  is_active BOOLEAN DEFAULT true,
  accessibility_features TEXT[], -- wheelchair, elderly care, etc.
  operating_hours JSONB,
  resource_levels JSONB, -- {'food': 80, 'water': 60, 'medical': 90} - percentage availability
  last_updated TIMESTAMP DEFAULT NOW(),
  government_unit_id UUID REFERENCES government_users(id)
);

-- Hazard zones and danger areas
CREATE TABLE hazard_zones (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  location POLYGON NOT NULL, -- Geographic area of hazard
  hazard_type VARCHAR NOT NULL, -- 'flood', 'fire', 'building_collapse', 'gas_leak', 'landslide'
  severity_level INTEGER CHECK (severity_level BETWEEN 1 AND 5), -- 1=low, 5=extreme
  description TEXT NOT NULL,
  safety_instructions TEXT,
  estimated_duration INTERVAL, -- How long hazard expected to last
  is_active BOOLEAN DEFAULT true,
  evacuation_required BOOLEAN DEFAULT false,
  alternative_routes JSONB, -- Suggested safe routes around the area
  created_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP,
  government_unit_id UUID REFERENCES government_users(id)
);

-- Safe routes and navigation
CREATE TABLE safe_routes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  route_name VARCHAR,
  start_point POINT NOT NULL,
  end_point POINT NOT NULL,
  waypoints JSONB, -- Array of coordinates for route
  route_status VARCHAR CHECK (route_status IN ('safe', 'caution', 'blocked', 'unknown')),
  estimated_travel_time INTERVAL,
  transportation_mode VARCHAR, -- 'walking', 'vehicle', 'bicycle'
  capacity_limit INTEGER, -- Max people who can use this route
  current_usage INTEGER DEFAULT 0,
  last_verified TIMESTAMP DEFAULT NOW(),
  government_unit_id UUID REFERENCES government_users(id)
);

-- Emergency services and facilities
CREATE TABLE emergency_facilities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  type VARCHAR NOT NULL, -- 'hospital', 'fire_station', 'police_station', 'pharmacy'
  location POINT NOT NULL,
  address TEXT NOT NULL,
  contact_info JSONB,
  services_available TEXT[], -- ['emergency_room', 'surgery', 'burn_unit', etc.]
  current_capacity INTEGER,
  operational_status VARCHAR CHECK (operational_status IN ('fully_operational', 'limited_capacity', 'emergency_only', 'closed')),
  last_status_update TIMESTAMP DEFAULT NOW()
);

-- Government announcements and alerts
CREATE TABLE government_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR NOT NULL,
  message TEXT NOT NULL,
  alert_type VARCHAR NOT NULL, -- 'evacuation', 'shelter_info', 'safety_warning', 'resource_update'
  severity VARCHAR CHECK (severity IN ('info', 'warning', 'urgent', 'critical')),
  target_area POLYGON, -- Geographic area for targeted alerts
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP,
  government_unit_id UUID REFERENCES government_users(id)
);
```

## Key Features to Implement

### 1. App Architecture
- Clean Architecture with Repository pattern
- MVVM pattern with Riverpod state management
- Dependency injection for testability
- Offline-first approach with local SQLite caching

### 2. Critical Functionalities

#### Automatic Disaster Detection Service
```dart
class AutoDisasterDetectionService {
  // Multi-source disaster monitoring (USGS, weather, seismic networks)
  // Automatic threshold-based activation
  // Government manual override capabilities
  // Cross-validation between multiple data sources
  // Automatic citizen notification and location tracking activation
}
```

#### Persistent Alarm System
```dart
class PersistentAlarmService { 
  // Continuous alarm that cannot be programmatically disabled
  // Only manual user action can stop the alarm
  // Bypasses device silent mode and do-not-disturb
  // Multiple alarm sounds and vibration patterns
  // Battery-conscious alarm management
}
```

#### Rescue Camp & Resource Management
```dart
class RescueCampService {
  // Government interface to add/update rescue camps
  // Real-time capacity and resource tracking
  // Citizen interface to find nearby camps with available resources
  // Resource level monitoring (food, water, medical supplies)
  // Facility information (wheelchair access, medical care, etc.)
}
```

#### Hazard Zone Management
```dart
class HazardZoneService {
  // Government marking of dangerous areas
  // Real-time hazard updates (flooding, fires, building collapse)
  // Automatic citizen alerts when approaching danger zones
  // Safe route calculation avoiding hazardous areas
  // Severity level management and evacuation requirements
}
```

#### Safe Navigation System
```dart
class SafeNavigationService {
  // Dynamic route planning avoiding hazard zones
  // Real-time route updates based on changing conditions
  // Alternative route suggestions when paths become unsafe
  // Crowd management to prevent overcrowding on routes
  // Integration with emergency facility locations
}
```

#### Government Alert Broadcasting
```dart
class GovernmentAlertService {
  // Mass communication system for safety announcements
  // Targeted alerts based on user location
  // Multi-language alert support
  // Priority messaging system (info, warning, urgent, critical)
  // Automatic alert expiration and updates
}
```

#### High-Precision Location Service
```dart
class PrecisionLocationService {
  // WhatsApp-style live location sharing
  // Sub-meter GPS accuracy when possible
  // Indoor positioning with WiFi/Bluetooth beacons
  // Reverse geocoding for exact addresses
  // Building floor and room detection
  // Automatic location broadcasting during disasters
}
```

#### Safety Status Manager
```dart
class SafetyStatusManager {
  // Handle safe/unsafe status changes
  // Auto-stop location sharing when safe
  // Emergency contact notifications
  // Integration with rescue dashboard
}
```

### 3. UI/UX Requirements
- **Citizen App**:
  - Real-time safety map with color-coded zones (green=safe, yellow=caution, red=danger)
  - Nearby rescue camps with live resource availability (food, water, medical, shelter)
  - Safe route navigation avoiding hazard zones
  - Emergency facility finder (hospitals, fire stations, police)
  - Government alerts and safety announcements
  - Resource availability notifications
  - Evacuation instructions and shelter information
- **Government Dashboard**:
  - Rescue camp management with resource tracking
  - Hazard zone creation and management tools
  - Mass alert broadcasting system
  - Citizen location monitoring with safety zone overlay
  - Resource distribution planning
  - Emergency facility status monitoring
  - Safe route management and crowd control
- **Rescue Team Interface**:
  - Assigned rescue locations with hazard awareness
  - Safe route planning to victim locations
  - Resource availability at nearby camps
  - Hazard zone alerts and safety protocols
- **Multi-language support for safety-critical information**
- **High contrast emergency mode for visibility**
- **Voice announcements for safety alerts**

### 4. Background Services
- **Automatic disaster monitoring** (every 15 seconds from multiple sources)
- **Mandatory location tracking** during active disasters (every 5 seconds for pinpoint accuracy)
- **Persistent alarm service** that cannot be disabled programmatically
- **Hazard zone monitoring** - Alert users approaching dangerous areas
- **Resource availability updates** - Real-time camp capacity and supply levels
- **Safe route calculation** - Continuous route optimization avoiding hazards
- **Government alert distribution** - Instant mass communication system
- **Battery optimization** while maintaining all critical safety functions
- **Network failure handling** with offline safety map caching
- **Crowd density monitoring** - Prevent overcrowding at rescue locations

### 5. Security & Privacy
- End-to-end encryption for location data
- Automatic data deletion after disaster period ends
- User consent for location sharing
- GDPR compliance for data handling
- Secure authentication with phone verification

### 6. Integration Requirements
- **Multi-Source Disaster APIs**: USGS, National Weather Service, local seismic networks, flood monitoring
- **Government Emergency Systems**: Integration with national/local emergency management and resource tracking
- **Navigation & Routing APIs**: Google Maps, OpenStreetMap with custom hazard avoidance
- **Resource Management Systems**: Integration with camp supply chains and inventory systems
- **Public Safety Networks**: Police, fire, medical emergency dispatch integration
- **Weather & Environmental APIs**: Real-time flood, fire, air quality monitoring
- **Traffic Management Systems**: Road closure and safe route coordination
- **Multi-language Translation APIs**: Safety alerts in local languages
- **Accessibility Services**: Support for users with disabilities
- **Offline Map Systems**: Critical safety information available without internet

## Development Phases

### Phase 1: Setup & Authentication
- Flutter project setup with clean architecture
- Supabase configuration and authentication
- Phone number verification system
- Basic user profile management

### Phase 2: Automatic Detection & Alarms
- Multi-source disaster API integration
- Automatic threshold-based disaster activation  
- Persistent alarm system (user-disable only)
- Government override and manual activation
- Cross-validation between data sources

### Phase 3: Precision Location Services
- WhatsApp-style live location sharing
- High-precision GPS with sub-meter accuracy
- Indoor positioning and building floor detection
- Automatic location broadcasting during disasters
- Reverse geocoding for exact addresses

### Phase 4: Resource & Safety Management
- Rescue camp management with live resource tracking
- Hazard zone mapping and citizen safety alerts
- Safe route planning with hazard avoidance
- Government alert broadcasting system
- Emergency facility status monitoring

### Phase 5: Advanced Safety Features
- Multi-language safety alert system
- Crowd management and capacity monitoring
- Offline safety map functionality
- Accessibility features for disabled users
- Integration with existing emergency management systems

## Specific Code Requests

Please provide complete, production-ready code for:

1. **Main App Structure**: Complete Flutter app with proper folder structure, state management setup, and navigation
2. **Supabase Integration**: Database setup, authentication, real-time subscriptions
3. **Location Services**: Background location tracking with battery optimization
4. **Disaster Monitoring**: API integration and background service implementation
5. **UI Components**: All screens with responsive design and accessibility features
6. **Testing**: Unit tests, integration tests, and widget tests

## CRITICAL SAFETY REQUIREMENTS

### Zero-Failure Tolerance
- **No false negatives**: System must NEVER fail to detect real disasters
- **No false positives**: Minimize unnecessary panic while maintaining safety
- **Redundant systems**: Multiple backup mechanisms for all critical functions
- **Fail-safe design**: If any component fails, default to maximum safety mode
- **Comprehensive error handling**: Every possible failure scenario must be handled gracefully
- **Real-time monitoring**: Continuous system health checks and automatic recovery

### Data Accuracy & Reliability
- **Cross-validation**: All critical data must be verified from multiple sources
- **Real-time verification**: Continuous validation of location data, resource availability
- **Government data integrity**: Secure, authenticated government updates only
- **Audit trails**: Complete logging of all critical system actions and decisions
- **Data synchronization**: Ensure all users see the same critical safety information
- **Backup communication**: Multiple communication channels (SMS, push, voice) for critical alerts

### User Safety Protection
- **Hazard prevention**: Actively prevent users from entering dangerous areas
- **Clear communication**: All safety instructions must be crystal clear and unambiguous
- **Multi-modal alerts**: Visual, audio, and haptic feedback for all critical alerts
- **Progressive warnings**: Escalating alert severity as users approach danger
- **Emergency override**: Government can override any user setting for safety
- **Accessibility compliance**: Full support for users with disabilities

## Expected Deliverables
1. Complete Flutter project with all source code
2. Supabase database schema and configuration files
3. API integration documentation
4. Deployment and setup instructions
5. User manual and technical documentation
6. Testing strategy and test cases

Start with the basic project structure and authentication system, then progressively build each feature. Focus on creating a robust, scalable solution that can handle real emergency situations with thousands of simultaneous users.