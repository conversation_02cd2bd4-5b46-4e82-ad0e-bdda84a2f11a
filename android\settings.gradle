pluginManagement {
    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
    plugins {
        id 'dev.flutter.flutter-gradle-plugin'
        id 'com.android.application' version '7.3.0'
        id 'com.android.library' version '7.3.0'
        id 'com.google.gms.google-services' version '4.4.1'
    }
}

dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.PREFER_SETTINGS)
    repositories {
        google()
        mavenCentral()
    }
}

include ':app'

localPropertiesFile = new File(rootProject.projectDir, "local.properties")
def properties = new Properties()

assert localPropertiesFile.exists()
localPropertiesFile.withReader("UTF-8") { reader -> properties.load(reader) }

def flutterSdkPath = properties.getProperty("flutter.sdk")
assert flutterSdkPath != null, "flutter.sdk not set in local.properties"

apply from: "${flutterSdkPath}/packages/flutter_tools/gradle/app_plugin_loader.gradle" 