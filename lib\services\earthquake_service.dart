import 'dart:convert';
import 'package:http/http.dart' as http;

class EarthquakeService {
  static const String _baseUrl = 'https://earthquake.usgs.gov/fdsnws/event/1/query';
  
  // Get recent earthquakes
  Future<Map<String, dynamic>> getRecentEarthquakes({
    double? minMagnitude,
    double? maxMagnitude,
    DateTime? startTime,
    DateTime? endTime,
    double? latitude,
    double? longitude,
    double? maxRadiusKm,
  }) async {
    try {
      final queryParams = {
        'format': 'geojson',
        if (minMagnitude != null) 'minmagnitude': minMagnitude.toString(),
        if (maxMagnitude != null) 'maxmagnitude': maxMagnitude.toString(),
        if (startTime != null) 'starttime': startTime.toIso8601String(),
        if (endTime != null) 'endtime': endTime.toIso8601String(),
        if (latitude != null) 'latitude': latitude.toString(),
        if (longitude != null) 'longitude': longitude.toString(),
        if (maxRadiusKm != null) 'maxradiuskm': maxRadiusKm.toString(),
      };

      final uri = Uri.parse(_baseUrl).replace(queryParameters: queryParams);
      final response = await http.get(uri);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to load earthquake data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching earthquake data: $e');
    }
  }

  // Get earthquakes in the last hour
  Future<Map<String, dynamic>> getLastHourEarthquakes() async {
    final endTime = DateTime.now();
    final startTime = endTime.subtract(const Duration(hours: 1));
    return getRecentEarthquakes(
      startTime: startTime,
      endTime: endTime,
      minMagnitude: 2.5, // Minimum magnitude to consider
    );
  }

  // Get significant earthquakes (magnitude >= 4.5)
  Future<Map<String, dynamic>> getSignificantEarthquakes() async {
    return getRecentEarthquakes(
      minMagnitude: 4.5,
      maxMagnitude: 10.0,
    );
  }

  // Get earthquakes near a specific location
  Future<Map<String, dynamic>> getNearbyEarthquakes({
    required double latitude,
    required double longitude,
    double maxRadiusKm = 100,
    double minMagnitude = 2.5,
  }) async {
    return getRecentEarthquakes(
      latitude: latitude,
      longitude: longitude,
      maxRadiusKm: maxRadiusKm,
      minMagnitude: minMagnitude,
    );
  }
} 