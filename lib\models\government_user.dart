class GovernmentUser {
  final String id;
  final String department;
  final int clearanceLevel;
  final bool canActivateDisasters;
  final bool canManageCamps;
  final bool canManageAlerts;
  final bool canViewAllUsers;
  final DateTime createdAt;
  final DateTime? lastLogin;

  GovernmentUser({
    required this.id,
    required this.department,
    required this.clearanceLevel,
    required this.canActivateDisasters,
    required this.canManageCamps,
    required this.canManageAlerts,
    required this.canViewAllUsers,
    required this.createdAt,
    this.lastLogin,
  });

  factory GovernmentUser.fromJson(Map<String, dynamic> json) {
    return GovernmentUser(
      id: json['id'],
      department: json['department'],
      clearanceLevel: json['clearance_level'],
      canActivateDisasters: json['can_activate_disasters'],
      canManageCamps: json['can_manage_camps'],
      canManageAlerts: json['can_manage_alerts'],
      canViewAllUsers: json['can_view_all_users'],
      createdAt: DateTime.parse(json['created_at']),
      lastLogin: json['last_login'] != null ? DateTime.parse(json['last_login']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'department': department,
      'clearance_level': clearanceLevel,
      'can_activate_disasters': canActivateDisasters,
      'can_manage_camps': canManageCamps,
      'can_manage_alerts': canManageAlerts,
      'can_view_all_users': canViewAllUsers,
      'created_at': createdAt.toIso8601String(),
      'last_login': lastLogin?.toIso8601String(),
    };
  }

  String get departmentDisplayName {
    switch (department) {
      case 'ndma':
        return 'National Disaster Management Authority';
      case 'sdma':
        return 'State Disaster Management Authority';
      case 'ddma':
        return 'District Disaster Management Authority';
      case 'police':
        return 'Police Department';
      case 'fire':
        return 'Fire Department';
      case 'medical':
        return 'Medical Emergency Services';
      default:
        return department.toUpperCase();
    }
  }

  String get clearanceLevelName {
    switch (clearanceLevel) {
      case 5:
        return 'National Level';
      case 4:
        return 'State Level';
      case 3:
        return 'District Level';
      case 2:
        return 'Local Authority';
      case 1:
        return 'Basic Access';
      default:
        return 'Unknown';
    }
  }

  // Mock government user for testing
  static GovernmentUser mockUser = GovernmentUser(
    id: '1',
    department: 'ndma',
    clearanceLevel: 5,
    canActivateDisasters: true,
    canManageCamps: true,
    canManageAlerts: true,
    canViewAllUsers: true,
    createdAt: DateTime.now(),
    lastLogin: DateTime.now(),
  );
}

class EmergencyFacility {
  final String id;
  final String name;
  final String type; // 'hospital', 'fire_station', 'police_station', 'pharmacy'
  final double latitude;
  final double longitude;
  final String address;
  final Map<String, dynamic> contactInfo;
  final List<String> servicesAvailable;
  final int? currentCapacity;
  final String operationalStatus; // 'fully_operational', 'limited_capacity', 'emergency_only', 'closed'
  final DateTime lastStatusUpdate;

  EmergencyFacility({
    required this.id,
    required this.name,
    required this.type,
    required this.latitude,
    required this.longitude,
    required this.address,
    required this.contactInfo,
    required this.servicesAvailable,
    this.currentCapacity,
    required this.operationalStatus,
    required this.lastStatusUpdate,
  });

  factory EmergencyFacility.fromJson(Map<String, dynamic> json) {
    // Parse location from PostGIS POINT format
    final locationStr = json['location'] as String?;
    double lat = 0.0, lng = 0.0;
    if (locationStr != null) {
      final coords = locationStr.replaceAll(RegExp(r'[POINT()]'), '').split(' ');
      if (coords.length >= 2) {
        lng = double.tryParse(coords[0]) ?? 0.0;
        lat = double.tryParse(coords[1]) ?? 0.0;
      }
    }

    return EmergencyFacility(
      id: json['id'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      latitude: lat,
      longitude: lng,
      address: json['address'] as String,
      contactInfo: Map<String, dynamic>.from(json['contact_info'] as Map? ?? {}),
      servicesAvailable: List<String>.from(json['services_available'] as List? ?? []),
      currentCapacity: json['current_capacity'] as int?,
      operationalStatus: json['operational_status'] as String,
      lastStatusUpdate: DateTime.parse(json['last_status_update'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'location': 'POINT($longitude $latitude)',
      'address': address,
      'contact_info': contactInfo,
      'services_available': servicesAvailable,
      'current_capacity': currentCapacity,
      'operational_status': operationalStatus,
      'last_status_update': lastStatusUpdate.toIso8601String(),
    };
  }

  String get typeDisplayName {
    switch (type) {
      case 'hospital':
        return 'Hospital';
      case 'fire_station':
        return 'Fire Station';
      case 'police_station':
        return 'Police Station';
      case 'pharmacy':
        return 'Pharmacy';
      default:
        return type;
    }
  }

  String get statusDisplayName {
    switch (operationalStatus) {
      case 'fully_operational':
        return 'Fully Operational';
      case 'limited_capacity':
        return 'Limited Capacity';
      case 'emergency_only':
        return 'Emergency Only';
      case 'closed':
        return 'Closed';
      default:
        return operationalStatus;
    }
  }

  bool get isOperational => operationalStatus != 'closed';
  bool get hasLimitedCapacity => operationalStatus == 'limited_capacity';
  bool get isEmergencyOnly => operationalStatus == 'emergency_only';
}

class GovernmentAlert {
  final String id;
  final String title;
  final String message;
  final String alertType; // 'evacuation', 'shelter_info', 'safety_warning', 'resource_update'
  final String severity; // 'info', 'warning', 'urgent', 'critical'
  final String? targetArea; // Geographic area for targeted alerts
  final bool isActive;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final String? governmentUnitId;

  GovernmentAlert({
    required this.id,
    required this.title,
    required this.message,
    required this.alertType,
    required this.severity,
    this.targetArea,
    required this.isActive,
    required this.createdAt,
    this.expiresAt,
    this.governmentUnitId,
  });

  factory GovernmentAlert.fromJson(Map<String, dynamic> json) {
    return GovernmentAlert(
      id: json['id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      alertType: json['alert_type'] as String,
      severity: json['severity'] as String,
      targetArea: json['target_area'] as String?,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      expiresAt: json['expires_at'] != null ? DateTime.parse(json['expires_at'] as String) : null,
      governmentUnitId: json['government_unit_id'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'alert_type': alertType,
      'severity': severity,
      'target_area': targetArea,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'government_unit_id': governmentUnitId,
    };
  }

  String get alertTypeDisplayName {
    switch (alertType) {
      case 'evacuation':
        return 'Evacuation Order';
      case 'shelter_info':
        return 'Shelter Information';
      case 'safety_warning':
        return 'Safety Warning';
      case 'resource_update':
        return 'Resource Update';
      default:
        return alertType;
    }
  }

  String get severityDisplayName {
    switch (severity) {
      case 'info':
        return 'Information';
      case 'warning':
        return 'Warning';
      case 'urgent':
        return 'Urgent';
      case 'critical':
        return 'Critical';
      default:
        return severity;
    }
  }

  bool get isCritical => severity == 'critical';
  bool get isUrgent => severity == 'urgent' || severity == 'critical';
  bool get isExpired => expiresAt != null && DateTime.now().isAfter(expiresAt!);
}
