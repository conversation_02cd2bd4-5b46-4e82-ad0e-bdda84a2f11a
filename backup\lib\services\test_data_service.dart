import 'package:supabase_flutter/supabase_flutter.dart';

class TestDataService {
  static final SupabaseClient _client = Supabase.instance.client;

  /// Create a test government user for the current authenticated user
  static Future<bool> createTestGovernmentUser() async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) return false;

      // First, check if user already exists in our users table
      final existingUser = await _client
          .from('users')
          .select('id')
          .eq('auth_user_id', user.id)
          .maybeSingle();

      String userId;
      if (existingUser == null) {
        // Create user record
        final userResponse = await _client
            .from('users')
            .insert({
              'auth_user_id': user.id,
              'phone': user.phone ?? '+91-9876543210',
              'name': user.userMetadata?['name'] ?? 'Test User',
              'emergency_contacts': ['+91-9123456789', '+91-9876543211'],
            })
            .select('id')
            .single();
        userId = userResponse['id'];
      } else {
        userId = existingUser['id'];
      }

      // Check if government user already exists
      final existingGovUser = await _client
          .from('government_users')
          .select('id')
          .eq('user_id', userId)
          .maybeSingle();

      if (existingGovUser == null) {
        // Create government user with admin privileges
        await _client.from('government_users').insert({
          'user_id': userId,
          'department': 'admin',
          'clearance_level': 5,
          'region_access': ['Delhi', 'NCR', 'All'],
          'can_activate_disasters': true,
        });
        print('Created government user for testing');
      } else {
        print('Government user already exists');
      }

      return true;
    } catch (e) {
      print('Error creating test government user: $e');
      return false;
    }
  }

  /// Create test disaster for demonstration
  static Future<bool> createTestDisaster() async {
    try {
      // Check if test disaster already exists
      final existing = await _client
          .from('disasters')
          .select('id')
          .eq('type', 'earthquake')
          .eq('is_active', true)
          .maybeSingle();

      if (existing == null) {
        await _client.from('disasters').insert({
          'type': 'earthquake',
          'location': 'POINT(77.2090 28.6139)', // Delhi coordinates
          'magnitude': 6.2,
          'radius_km': 100,
          'is_active': true,
          'activation_source': 'automatic',
          'description': 'Test earthquake for demonstration - Magnitude 6.2 detected near Central Delhi',
        });
      }

      return true;
    } catch (e) {
      print('Error creating test disaster: $e');
      return false;
    }
  }

  /// Create test alerts
  static Future<bool> createTestAlerts() async {
    try {
      // Create a critical alert
      await _client.from('government_alerts').insert({
        'title': 'Emergency Evacuation Notice',
        'message': 'Immediate evacuation required for Central Delhi area due to earthquake. Proceed to nearest evacuation center.',
        'alert_type': 'evacuation',
        'severity': 'critical',
        'expires_at': DateTime.now().add(const Duration(hours: 6)).toIso8601String(),
      });

      // Create an info alert
      await _client.from('government_alerts').insert({
        'title': 'Shelter Information Update',
        'message': 'Additional emergency shelters have been opened. Check the Camps section for latest availability.',
        'alert_type': 'shelter_info',
        'severity': 'info',
        'expires_at': DateTime.now().add(const Duration(hours: 12)).toIso8601String(),
      });

      return true;
    } catch (e) {
      print('Error creating test alerts: $e');
      return false;
    }
  }

  /// Initialize all test data
  static Future<void> initializeTestData() async {
    try {
      print('Initializing test data...');

      await createTestGovernmentUser();
      await createTestDisaster();
      await createTestAlerts();

      print('Test data initialization completed');
    } catch (e) {
      print('Error initializing test data: $e');
    }
  }

  /// Check if user has government access
  static Future<bool> hasGovernmentAccess() async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) return false;

      final govUser = await _client
          .from('government_users')
          .select('id')
          .eq('user_id', user.id)
          .maybeSingle();

      return govUser != null;
    } catch (e) {
      return false;
    }
  }
}
