import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/camps_provider.dart';
import 'camp_details_screen.dart';

class CampsScreen extends ConsumerWidget {
  const CampsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final campsAsync = ref.watch(campsProvider);
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      body: Safe<PERSON><PERSON>(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16.0),
                child: Text('Relief Camps',
                    style: textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)),
              ),
              // Search bar
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TextField(
                  decoration: InputDecoration(
                    hintText: 'Search for camps...',
                    prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
                    border: InputBorder.none, // Use theme's border or remove if container provides it
                    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                  ),
                  style: textTheme.bodyLarge,
                ),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: campsAsync.when(
                  data: (camps) => ListView.separated(
                    itemCount: camps.length,
                    separatorBuilder: (_, __) => const SizedBox(height: 8),
                    itemBuilder: (context, i) {
                      final camp = camps[i];
                      return Card(
                        child: ListTile(
                          leading: Icon(Icons.cabin_rounded, color: Theme.of(context).colorScheme.primary, size: 32),
                          title: Text(camp.name, style: textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                          subtitle: Text('Capacity: ${camp.currentOccupancy}/${camp.capacity}\nFood: ${camp.getResourceStatus('food')} | Water: ${camp.getResourceStatus('water')}', style: textTheme.bodySmall),
                          trailing: Chip(
                            label: Text(camp.isActive ? 'Open' : 'Closed', style: textTheme.labelSmall?.copyWith(color: Colors.white, fontWeight: FontWeight.bold)),
                            backgroundColor: camp.isActive ? Colors.green : Colors.red,
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          ),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (_) => CampDetailsScreen(camp: camp),
                              ),
                            );
                          },
                          contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                        ),
                      );
                    },
                  ),
                  loading: () => const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(),
                        SizedBox(height: 16),
                        Text('Loading rescue camps...'),
                      ],
                    ),
                  ),
                  error: (error, stack) => Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, size: 64, color: Colors.red),
                        SizedBox(height: 16),
                        Text('Error loading camps'),
                        SizedBox(height: 8),
                        Text('$error', style: TextStyle(fontSize: 12, color: Colors.grey)),
                        SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => ref.refresh(campsProvider),
                          child: Text('Retry'),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}