import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../services/camps_service.dart';
import '../models/camp.dart';

class CampsScreen extends ConsumerWidget {
  const CampsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Relief Camps'),
      ),
      body: const Center(
        child: Text('Relief Camps Screen'),
      ),
    );
  }
}