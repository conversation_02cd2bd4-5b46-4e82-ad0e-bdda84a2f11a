import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/government_service.dart';
import '../../models/camp.dart';

class CampManagementScreen extends ConsumerStatefulWidget {
  const CampManagementScreen({super.key});

  @override
  ConsumerState<CampManagementScreen> createState() => _CampManagementScreenState();
}

class _CampManagementScreenState extends ConsumerState<CampManagementScreen> {
  List<Camp> camps = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCamps();
  }

  Future<void> _loadCamps() async {
    setState(() => isLoading = true);
    try {
      // TODO: Load camps from government service
      setState(() {
        camps = Camp.mockList(); // Temporary mock data
        isLoading = false;
      });
    } catch (e) {
      setState(() => isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading camps: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Camp Management'),
        backgroundColor: Colors.green.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showCreateCampDialog,
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadCamps,
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: camps.length,
                itemBuilder: (context, index) {
                  final camp = camps[index];
                  return _buildCampCard(camp);
                },
              ),
            ),
    );
  }

  Widget _buildCampCard(Camp camp) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        camp.name,
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
                      Text(
                        camp.type.replaceAll('_', ' ').toUpperCase(),
                        style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: camp.isActive ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    camp.isActive ? 'ACTIVE' : 'INACTIVE',
                    style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem('Capacity', '${camp.currentOccupancy}/${camp.capacity}'),
                ),
                Expanded(
                  child: _buildInfoItem('Food', camp.getResourceStatus('food')),
                ),
                Expanded(
                  child: _buildInfoItem('Water', camp.getResourceStatus('water')),
                ),
                Expanded(
                  child: _buildInfoItem('Medical', camp.getResourceStatus('medical')),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showUpdateResourcesDialog(camp),
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('Update Resources'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showUpdateOccupancyDialog(camp),
                    icon: const Icon(Icons.people, size: 16),
                    label: const Text('Update Occupancy'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  void _showCreateCampDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Camp'),
        content: const Text('Camp creation form will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement camp creation
            },
            child: const Text('Create'),
          ),
        ],
      ),
    );
  }

  void _showUpdateResourcesDialog(Camp camp) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Update Resources - ${camp.name}'),
        content: const Text('Resource update form will be implemented here.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement resource update
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  void _showUpdateOccupancyDialog(Camp camp) {
    final controller = TextEditingController(text: camp.currentOccupancy.toString());
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Update Occupancy - ${camp.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Current: ${camp.currentOccupancy}/${camp.capacity}'),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'New Occupancy',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final newOccupancy = int.tryParse(controller.text);
              if (newOccupancy != null && newOccupancy >= 0 && newOccupancy <= camp.capacity) {
                Navigator.pop(context);
                final success = await GovernmentService.updateCampOccupancy(camp.id, newOccupancy);
                if (success) {
                  _loadCamps();
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Occupancy updated successfully')),
                    );
                  }
                } else {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Failed to update occupancy')),
                    );
                  }
                }
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Invalid occupancy value')),
                );
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }
}
