-- AapatMitra Database Schema
-- Execute these commands in your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Users table (extends auth.users)
CREATE TABLE public.users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  auth_user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  phone VARCHAR UNIQUE NOT NULL,
  name VARCHAR NOT NULL,
  emergency_contacts JSONB DEFAULT '[]',
  building_info JSONB DEFAULT '{}', -- floor, apartment, building name
  is_government_user BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Disasters table
CREATE TABLE public.disasters (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  type VARCHAR NOT NULL, -- 'earthquake', 'flood', etc.
  location POINT NOT NULL,
  magnitude DECIMAL,
  radius_km INTEGER,
  is_active BOOLEAN DEFAULT true,
  activation_source VARCHAR DEFAULT 'automatic', -- 'automatic', 'government', 'manual'
  government_activated BOOLEAN DEFAULT false,
  description TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Real-time user locations (WhatsApp-style)
CREATE TABLE public.live_user_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  disaster_id UUID REFERENCES public.disasters(id) ON DELETE CASCADE,
  latitude DECIMAL(10,8) NOT NULL, -- High precision for pinpoint accuracy
  longitude DECIMAL(11,8) NOT NULL,
  altitude DECIMAL,
  accuracy_meters DECIMAL,
  address TEXT, -- Reverse geocoded address
  building_floor INTEGER,
  indoor_location JSONB, -- WiFi/Bluetooth beacon data
  status VARCHAR CHECK (status IN ('safe', 'unsafe', 'no_response', 'needs_help')) DEFAULT 'no_response',
  alarm_status VARCHAR CHECK (alarm_status IN ('ringing', 'acknowledged', 'disabled_by_user')) DEFAULT 'ringing',
  last_updated TIMESTAMP DEFAULT NOW(),
  battery_level INTEGER,
  network_status VARCHAR
);

-- Government users and permissions
CREATE TABLE public.government_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  department VARCHAR NOT NULL, -- 'fire', 'police', 'medical', 'admin'
  clearance_level INTEGER DEFAULT 1, -- 1-5, higher = more access
  region_access TEXT[] DEFAULT '{}', -- Geographic areas they can monitor
  can_activate_disasters BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Rescue camps and evacuation centers
CREATE TABLE public.rescue_camps (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  location POINT NOT NULL,
  address TEXT NOT NULL,
  type VARCHAR NOT NULL, -- 'evacuation_center', 'medical_camp', 'food_distribution', 'shelter'
  capacity INTEGER NOT NULL,
  current_occupancy INTEGER DEFAULT 0,
  facilities JSONB DEFAULT '[]', -- ['food', 'water', 'medical', 'shelter', 'power', 'wifi']
  contact_info JSONB DEFAULT '{}',
  is_active BOOLEAN DEFAULT true,
  accessibility_features TEXT[] DEFAULT '{}', -- wheelchair, elderly care, etc.
  operating_hours JSONB DEFAULT '{}',
  resource_levels JSONB DEFAULT '{}', -- {'food': 80, 'water': 60, 'medical': 90} - percentage availability
  last_updated TIMESTAMP DEFAULT NOW(),
  government_unit_id UUID REFERENCES public.government_users(id)
);

-- Hazard zones and danger areas
CREATE TABLE public.hazard_zones (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  location POLYGON NOT NULL, -- Geographic area of hazard
  hazard_type VARCHAR NOT NULL, -- 'flood', 'fire', 'building_collapse', 'gas_leak', 'landslide'
  severity_level INTEGER CHECK (severity_level BETWEEN 1 AND 5) DEFAULT 1, -- 1=low, 5=extreme
  description TEXT NOT NULL,
  safety_instructions TEXT,
  estimated_duration INTERVAL, -- How long hazard expected to last
  is_active BOOLEAN DEFAULT true,
  evacuation_required BOOLEAN DEFAULT false,
  alternative_routes JSONB DEFAULT '[]', -- Suggested safe routes around the area
  created_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP,
  government_unit_id UUID REFERENCES public.government_users(id)
);

-- Safe routes and navigation
CREATE TABLE public.safe_routes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  route_name VARCHAR,
  start_point POINT NOT NULL,
  end_point POINT NOT NULL,
  waypoints JSONB DEFAULT '[]', -- Array of coordinates for route
  route_status VARCHAR CHECK (route_status IN ('safe', 'caution', 'blocked', 'unknown')) DEFAULT 'unknown',
  estimated_travel_time INTERVAL,
  transportation_mode VARCHAR DEFAULT 'walking', -- 'walking', 'vehicle', 'bicycle'
  capacity_limit INTEGER,
  current_usage INTEGER DEFAULT 0,
  last_verified TIMESTAMP DEFAULT NOW(),
  government_unit_id UUID REFERENCES public.government_users(id)
);

-- Emergency facilities
CREATE TABLE public.emergency_facilities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR NOT NULL,
  type VARCHAR NOT NULL, -- 'hospital', 'fire_station', 'police_station', 'pharmacy'
  location POINT NOT NULL,
  address TEXT NOT NULL,
  contact_info JSONB DEFAULT '{}',
  services_available TEXT[] DEFAULT '{}', -- ['emergency_room', 'surgery', 'burn_unit', etc.]
  current_capacity INTEGER,
  operational_status VARCHAR CHECK (operational_status IN ('fully_operational', 'limited_capacity', 'emergency_only', 'closed')) DEFAULT 'fully_operational',
  last_status_update TIMESTAMP DEFAULT NOW()
);

-- Government announcements and alerts
CREATE TABLE public.government_alerts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR NOT NULL,
  message TEXT NOT NULL,
  alert_type VARCHAR NOT NULL, -- 'evacuation', 'shelter_info', 'safety_warning', 'resource_update'
  severity VARCHAR CHECK (severity IN ('info', 'warning', 'urgent', 'critical')) DEFAULT 'info',
  target_area POLYGON, -- Geographic area for targeted alerts
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP,
  government_unit_id UUID REFERENCES public.government_users(id)
);

-- Function to get nearby camps
CREATE OR REPLACE FUNCTION get_nearby_camps(user_lat DECIMAL, user_lng DECIMAL, radius_km INTEGER)
RETURNS TABLE (
  id UUID,
  name VARCHAR,
  address TEXT,
  type VARCHAR,
  capacity INTEGER,
  current_occupancy INTEGER,
  facilities JSONB,
  contact_info JSONB,
  is_active BOOLEAN,
  accessibility_features TEXT[],
  operating_hours JSONB,
  resource_levels JSONB,
  last_updated TIMESTAMP,
  location TEXT,
  distance_km DECIMAL
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    rc.id,
    rc.name,
    rc.address,
    rc.type,
    rc.capacity,
    rc.current_occupancy,
    rc.facilities,
    rc.contact_info,
    rc.is_active,
    rc.accessibility_features,
    rc.operating_hours,
    rc.resource_levels,
    rc.last_updated,
    ST_AsText(rc.location) as location,
    ST_Distance(
      ST_GeogFromText('POINT(' || user_lng || ' ' || user_lat || ')'),
      ST_GeogFromText(ST_AsText(rc.location))
    ) / 1000 as distance_km
  FROM public.rescue_camps rc
  WHERE rc.is_active = true
    AND ST_DWithin(
      ST_GeogFromText('POINT(' || user_lng || ' ' || user_lat || ')'),
      ST_GeogFromText(ST_AsText(rc.location)),
      radius_km * 1000
    )
  ORDER BY distance_km ASC;
END;
$$ LANGUAGE plpgsql;
