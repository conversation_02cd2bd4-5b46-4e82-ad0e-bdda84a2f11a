class SafeRoute {
  final String id;
  final String routeName;
  final String status; // 'safe', 'caution', 'blocked', 'unknown'
  final String estimatedTime;

  SafeRoute({
    required this.id,
    required this.routeName,
    required this.status,
    required this.estimatedTime,
  });

  // Mock safe routes
  static List<SafeRoute> mockList() => [
    SafeRoute(id: '1', routeName: 'Route 1', status: 'safe', estimatedTime: '15 min'),
    SafeRoute(id: '2', routeName: 'Route 2', status: 'caution', estimatedTime: '20 min'),
    SafeRoute(id: '3', routeName: 'Route 3', status: 'blocked', estimatedTime: '--'),
  ];
} 