class WeatherData {
  final double temperature;
  final String condition;
  final double windSpeed;
  final int humidity;
  final double feelsLike;
  final double uvIndex;
  final double airQualityIndex;
  final List<WeatherAlert> alerts;

  WeatherData({
    required this.temperature,
    required this.condition,
    required this.windSpeed,
    required this.humidity,
    required this.feelsLike,
    required this.uvIndex,
    required this.airQualityIndex,
    required this.alerts,
  });

  factory WeatherData.fromJson(Map<String, dynamic> json) {
    final current = json['current'];
    final alerts = (json['alerts']?['alert'] as List? ?? [])
        .map((alert) => WeatherAlert.fromJson(alert))
        .toList();

    return WeatherData(
      temperature: current['temp_c'].toDouble(),
      condition: current['condition']['text'],
      windSpeed: current['wind_kph'].toDouble(),
      humidity: current['humidity'],
      feelsLike: current['feelslike_c'].toDouble(),
      uvIndex: current['uv'].toDouble(),
      airQualityIndex: current['air_quality']?['us-epa-index']?.toDouble() ?? 0.0,
      alerts: alerts,
    );
  }
}

class WeatherAlert {
  final String headline;
  final String severity;
  final String message;
  final DateTime startTime;
  final DateTime endTime;

  WeatherAlert({
    required this.headline,
    required this.severity,
    required this.message,
    required this.startTime,
    required this.endTime,
  });

  factory WeatherAlert.fromJson(Map<String, dynamic> json) {
    return WeatherAlert(
      headline: json['headline'] ?? '',
      severity: json['severity'] ?? '',
      message: json['msg'] ?? '',
      startTime: DateTime.parse(json['start']),
      endTime: DateTime.parse(json['end']),
    );
  }

  bool get isSevere => severity == 'Extreme' || severity == 'Severe';
} 