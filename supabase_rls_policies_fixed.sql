-- Fixed Row Level Security Policies for AapatMitra
-- Execute these commands to fix the infinite recursion issue

-- First, drop existing problematic policies
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.users;
DROP POLICY IF EXISTS "Government users can view all users during disasters" ON public.users;

-- Create corrected policies for users table
CREATE POLICY "Users can view their own profile" ON public.users
  FOR SELECT USING (auth.uid() = auth_user_id);

CREATE POLICY "Users can update their own profile" ON public.users
  FOR UPDATE USING (auth.uid() = auth_user_id);

CREATE POLICY "Users can insert their own profile" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = auth_user_id);

-- Simplified government access policy (no recursion)
CREATE POLICY "Government users can view all users" ON public.users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.government_users 
      WHERE user_id IN (
        SELECT id FROM public.users 
        WHERE auth_user_id = auth.uid()
      )
    )
  );

-- Fix government_users policies to avoid recursion
DROP POLICY IF EXISTS "Government users can manage disasters" ON public.disasters;
DROP POLICY IF EXISTS "Government users can view all locations during disasters" ON public.live_user_locations;
DROP POLICY IF EXISTS "Government users can manage rescue camps" ON public.rescue_camps;
DROP POLICY IF EXISTS "Government users can manage hazard zones" ON public.hazard_zones;
DROP POLICY IF EXISTS "Government users can manage safe routes" ON public.safe_routes;
DROP POLICY IF EXISTS "Government users can manage emergency facilities" ON public.emergency_facilities;
DROP POLICY IF EXISTS "Government users can manage alerts" ON public.government_alerts;

-- Recreate government policies with direct auth check
CREATE POLICY "Government users can manage disasters" ON public.disasters
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      WHERE gu.user_id IN (
        SELECT u.id FROM public.users u WHERE u.auth_user_id = auth.uid()
      ) AND gu.can_activate_disasters = true
    )
  );

CREATE POLICY "Government users can view all locations during disasters" ON public.live_user_locations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      WHERE gu.user_id IN (
        SELECT u.id FROM public.users u WHERE u.auth_user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Government users can manage rescue camps" ON public.rescue_camps
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      WHERE gu.user_id IN (
        SELECT u.id FROM public.users u WHERE u.auth_user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Government users can manage hazard zones" ON public.hazard_zones
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      WHERE gu.user_id IN (
        SELECT u.id FROM public.users u WHERE u.auth_user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Government users can manage safe routes" ON public.safe_routes
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      WHERE gu.user_id IN (
        SELECT u.id FROM public.users u WHERE u.auth_user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Government users can manage emergency facilities" ON public.emergency_facilities
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      WHERE gu.user_id IN (
        SELECT u.id FROM public.users u WHERE u.auth_user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Government users can manage alerts" ON public.government_alerts
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      WHERE gu.user_id IN (
        SELECT u.id FROM public.users u WHERE u.auth_user_id = auth.uid()
      )
    )
  );

-- Add policy for government_users table itself
CREATE POLICY "Users can view their government role" ON public.government_users
  FOR SELECT USING (
    user_id IN (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

-- Ensure RLS is enabled
ALTER TABLE public.government_users ENABLE ROW LEVEL SECURITY;
