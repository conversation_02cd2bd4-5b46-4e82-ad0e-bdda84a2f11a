import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../services/government_service.dart';
import '../../models/camp.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class CampManagementScreen extends ConsumerWidget {
  const CampManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Camp Management'),
      ),
      body: const Center(
        child: Text('Camp Management Screen'),
      ),
    );
  }
}

class _CampManagementScreenState extends State<CampManagementScreen> {
  bool isLoading = false;
  List<Map<String, dynamic>> camps = [];
  final Set<Marker> _markers = {};
  GoogleMapController? _mapController;

  @override
  void initState() {
    super.initState();
    _loadCamps();
  }

  Future<void> _loadCamps() async {
    setState(() => isLoading = true);

    try {
      final activeCamps = await GovernmentService.getActiveCamps();
      setState(() {
        camps = activeCamps;
        _updateMapMarkers();
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }

    setState(() => isLoading = false);
  }

  void _updateMapMarkers() {
    _markers.clear();

    for (var camp in camps) {
      final LatLng position = LatLng(
        camp['latitude'] as double,
        camp['longitude'] as double,
      );

      _markers.add(
        Marker(
          markerId: MarkerId(camp['id'].toString()),
          position: position,
          infoWindow: InfoWindow(
            title: camp['name'],
            snippet: 'Capacity: ${camp['capacity']}',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
        ),
      );
    }

    if (_mapController != null && camps.isNotEmpty) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(
          _getBounds(_markers.map((m) => m.position).toList()),
          50.0,
        ),
      );
    }
  }

  LatLngBounds _getBounds(List<LatLng> points) {
    double minLat = points.first.latitude;
    double maxLat = points.first.latitude;
    double minLng = points.first.longitude;
    double maxLng = points.first.longitude;

    for (var point in points) {
      if (point.latitude < minLat) minLat = point.latitude;
      if (point.latitude > maxLat) maxLat = point.latitude;
      if (point.longitude < minLng) minLng = point.longitude;
      if (point.longitude > maxLng) maxLng = point.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  Future<void> _addCamp() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => const _AddCampDialog(),
    );

    if (result != null) {
      setState(() => isLoading = true);

      try {
        final success = await GovernmentService.createReliefCamp(
          name: result['name'],
          address: result['address'],
          latitude: result['latitude'],
          longitude: result['longitude'],
          capacity: result['capacity'],
          facilities: result['facilities'],
        );

        if (success) {
          await _loadCamps();
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Failed to create camp')),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: $e')),
          );
        }
      }

      setState(() => isLoading = false);
    }
  }

  Future<void> _updateCampStatus(String campId, bool isActive) async {
    setState(() => isLoading = true);

    try {
      final success = await GovernmentService.updateCampStatus(campId, isActive);
      if (success) {
        await _loadCamps();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to update camp status')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }

    setState(() => isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Camp Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadCamps,
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                SizedBox(
                  height: 300,
                  child: GoogleMap(
                    initialCameraPosition: const CameraPosition(
                      target: LatLng(20.5937, 78.9629), // Center of India
                      zoom: 5,
                    ),
                    markers: _markers,
                    onMapCreated: (controller) => _mapController = controller,
                  ),
                ),
                Expanded(
                  child: camps.isEmpty
                      ? const Center(
                          child: Text(
                            'No active camps',
                            style: TextStyle(fontSize: 16),
                          ),
                        )
                      : ListView.builder(
                          itemCount: camps.length,
                          itemBuilder: (context, index) {
                            final camp = camps[index];
                            return Card(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              child: ListTile(
                                leading: const Icon(
                                  Icons.cabin,
                                  color: Colors.green,
                                ),
                                title: Text(
                                  camp['name'],
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(camp['address']),
                                    Text(
                                      'Capacity: ${camp['capacity']}',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                                trailing: IconButton(
                                  icon: const Icon(Icons.close),
                                  color: Colors.red,
                                  onPressed: () => _updateCampStatus(
                                    camp['id'].toString(),
                                    false,
                                  ),
                                ),
                                onTap: () => _showCampDetails(camp),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addCamp,
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showCampDetails(Map<String, dynamic> camp) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(camp['name']),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Address: ${camp['address']}'),
              const SizedBox(height: 8),
              Text('Capacity: ${camp['capacity']}'),
              const SizedBox(height: 8),
              const Text(
                'Facilities:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              ...(camp['facilities'] as Map<String, dynamic>).entries.map(
                (e) => Text('${e.key}: ${e.value}'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

class _AddCampDialog extends StatefulWidget {
  const _AddCampDialog();

  @override
  State<_AddCampDialog> createState() => _AddCampDialogState();
}

class _AddCampDialogState extends State<_AddCampDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _addressController = TextEditingController();
  final _latitudeController = TextEditingController();
  final _longitudeController = TextEditingController();
  final _capacityController = TextEditingController();
  final Map<String, bool> _facilities = {
    'Water Supply': false,
    'Electricity': false,
    'Medical Facilities': false,
    'Food Service': false,
    'Sanitation': false,
    'Security': false,
  };

  @override
  void dispose() {
    _nameController.dispose();
    _addressController.dispose();
    _latitudeController.dispose();
    _longitudeController.dispose();
    _capacityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Relief Camp'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'Name'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _addressController,
                decoration: const InputDecoration(labelText: 'Address'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter an address';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _latitudeController,
                decoration: const InputDecoration(labelText: 'Latitude'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter latitude';
                  }
                  final latitude = double.tryParse(value);
                  if (latitude == null || latitude < -90 || latitude > 90) {
                    return 'Invalid latitude';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _longitudeController,
                decoration: const InputDecoration(labelText: 'Longitude'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter longitude';
                  }
                  final longitude = double.tryParse(value);
                  if (longitude == null || longitude < -180 || longitude > 180) {
                    return 'Invalid longitude';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _capacityController,
                decoration: const InputDecoration(labelText: 'Capacity'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter capacity';
                  }
                  final capacity = int.tryParse(value);
                  if (capacity == null || capacity <= 0) {
                    return 'Invalid capacity';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              const Text(
                'Facilities',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ..._facilities.entries.map(
                (e) => CheckboxListTile(
                  title: Text(e.key),
                  value: e.value,
                  onChanged: (value) {
                    setState(() => _facilities[e.key] = value!);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              Navigator.pop(context, {
                'name': _nameController.text,
                'address': _addressController.text,
                'latitude': double.parse(_latitudeController.text),
                'longitude': double.parse(_longitudeController.text),
                'capacity': int.parse(_capacityController.text),
                'facilities': _facilities,
              });
            }
          },
          child: const Text('Add'),
        ),
      ],
    );
  }
}
