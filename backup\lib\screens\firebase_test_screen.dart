import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/firebase_service.dart';
import '../utils/fcm_token_generator.dart';

class FirebaseTestScreen extends StatefulWidget {
  const FirebaseTestScreen({super.key});

  @override
  State<FirebaseTestScreen> createState() => _FirebaseTestScreenState();
}

class _FirebaseTestScreenState extends State<FirebaseTestScreen> {
  String? _fcmToken;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadFCMToken();
  }

  Future<void> _loadFCMToken() async {
    setState(() {
      _isLoading = true;
    });

    try {
      String? token = await FirebaseService.getFCMToken();
      if (token == null) {
        // Generate mock token for testing if Firebase is not available
        token = FCMTokenGenerator.generateMockToken();
      }
      setState(() {
        _fcmToken = token;
        _isLoading = false;
      });
    } catch (e) {
      // Fallback to mock token
      String mockToken = FCMTokenGenerator.generateMockToken();
      setState(() {
        _fcmToken = mockToken;
        _isLoading = false;
      });
    }
  }

  void _copyToClipboard() {
    if (_fcmToken != null && !_fcmToken!.startsWith('Error:')) {
      Clipboard.setData(ClipboardData(text: _fcmToken!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('FCM Token copied to clipboard!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Firebase FCM Token'),
        backgroundColor: const Color(0xFFD9BBA0),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🔥 Firebase FCM Token for Testing',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Copy this token to Firebase Console to test notifications:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              )
            else if (_fcmToken != null)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'FCM Token:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    SelectableText(
                      _fcmToken!,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              )
            else
              const Text(
                'No FCM token available. Make sure Firebase is configured.',
                style: TextStyle(color: Colors.red),
              ),
            const SizedBox(height: 16),
            if (_fcmToken != null && !_fcmToken!.startsWith('Error:'))
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _copyToClipboard,
                  icon: const Icon(Icons.copy),
                  label: const Text('Copy Token'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFD9BBA0),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _loadFCMToken,
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh Token'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 16),
            const Text(
              '📋 Instructions:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '1. Copy the FCM token above\n'
              '2. Go to Firebase Console\n'
              '3. Navigate to Cloud Messaging\n'
              '4. Click "Send your first message"\n'
              '5. Paste the token in "Test on device" section\n'
              '6. Send the emergency notification!',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}
