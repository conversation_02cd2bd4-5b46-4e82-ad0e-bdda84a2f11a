import 'dart:math' as dart_math;
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/camp.dart';

class CampsService {
  static final SupabaseClient _client = Supabase.instance.client;

  // Get all active rescue camps
  static Future<List<Camp>> getActiveCamps() async {
    try {
      final response = await _client
          .from('rescue_camps')
          .select('*, ST_AsText(location) as location')
          .eq('is_active', true)
          .order('last_updated', ascending: false);

      return (response as List)
          .map((json) => Camp.fromJson(json))
          .toList();
    } catch (e) {
      print('Error fetching camps: $e');
      // Return mock data as fallback
      return Camp.mockList();
    }
  }

  // Get camps by type
  static Future<List<Camp>> getCampsByType(String type) async {
    try {
      final response = await _client
          .from('rescue_camps')
          .select('*, ST_AsText(location) as location')
          .eq('is_active', true)
          .eq('type', type)
          .order('last_updated', ascending: false);

      return (response as List)
          .map((json) => Camp.fromJson(json))
          .toList();
    } catch (e) {
      print('Error fetching camps by type: $e');
      return Camp.mockList().where((camp) => camp.type == type).toList();
    }
  }

  // Get nearby camps within radius (in kilometers)
  static Future<List<Camp>> getNearbyCamps(double latitude, double longitude, double radiusKm) async {
    try {
      // Using PostGIS ST_DWithin for geographic distance calculation
      final response = await _client
          .rpc('get_nearby_camps', params: {
            'user_lat': latitude,
            'user_lng': longitude,
            'radius_km': radiusKm,
          });

      return (response as List)
          .map((json) => Camp.fromJson(json))
          .toList();
    } catch (e) {
      print('Error fetching nearby camps: $e');
      // Fallback to simple distance calculation
      return _filterCampsByDistance(Camp.mockList(), latitude, longitude, radiusKm);
    }
  }

  // Get camp by ID
  static Future<Camp?> getCampById(String id) async {
    try {
      final response = await _client
          .from('rescue_camps')
          .select('*, ST_AsText(location) as location')
          .eq('id', id)
          .single();

      return Camp.fromJson(response);
    } catch (e) {
      print('Error fetching camp by ID: $e');
      return Camp.mockList().firstWhere((camp) => camp.id == id);
    }
  }

  // Update camp occupancy (for government users)
  static Future<bool> updateCampOccupancy(String campId, int newOccupancy) async {
    try {
      await _client
          .from('rescue_camps')
          .update({
            'current_occupancy': newOccupancy,
            'last_updated': DateTime.now().toIso8601String(),
          })
          .eq('id', campId);

      return true;
    } catch (e) {
      print('Error updating camp occupancy: $e');
      return false;
    }
  }

  // Update resource levels (for government users)
  static Future<bool> updateResourceLevels(String campId, Map<String, int> resourceLevels) async {
    try {
      await _client
          .from('rescue_camps')
          .update({
            'resource_levels': resourceLevels,
            'last_updated': DateTime.now().toIso8601String(),
          })
          .eq('id', campId);

      return true;
    } catch (e) {
      print('Error updating resource levels: $e');
      return false;
    }
  }

  // Create new camp (for government users)
  static Future<Camp?> createCamp(Camp camp) async {
    try {
      final response = await _client
          .from('rescue_camps')
          .insert(camp.toJson())
          .select('*, ST_AsText(location) as location')
          .single();

      return Camp.fromJson(response);
    } catch (e) {
      print('Error creating camp: $e');
      return null;
    }
  }

  // Subscribe to real-time camp updates
  static Stream<List<Camp>> subscribeToCampUpdates() {
    return _client
        .from('rescue_camps')
        .stream(primaryKey: ['id'])
        .eq('is_active', true)
        .map((data) => data.map((json) => Camp.fromJson(json)).toList());
  }

  // Subscribe to specific camp updates
  static Stream<Camp?> subscribeToCampById(String campId) {
    return _client
        .from('rescue_camps')
        .stream(primaryKey: ['id'])
        .eq('id', campId)
        .map((data) => data.isNotEmpty ? Camp.fromJson(data.first) : null);
  }

  // Helper method for distance calculation fallback
  static List<Camp> _filterCampsByDistance(List<Camp> camps, double userLat, double userLng, double radiusKm) {
    return camps.where((camp) {
      final distance = _calculateDistance(userLat, userLng, camp.latitude, camp.longitude);
      return distance <= radiusKm;
    }).toList();
  }

  // Simple distance calculation using Haversine formula
  static double _calculateDistance(double lat1, double lng1, double lat2, double lng2) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    final double dLat = _degreesToRadians(lat2 - lat1);
    final double dLng = _degreesToRadians(lng2 - lng1);

    final double a =
        (dLat / 2).sin() * (dLat / 2).sin() +
        lat1.cos() * lat2.cos() *
        (dLng / 2).sin() * (dLng / 2).sin();

    final double c = 2 * a.sqrt().asin();

    return earthRadius * c;
  }

  static double _degreesToRadians(double degrees) {
    return degrees * (3.14159265359 / 180);
  }
}

// Extension for math functions
extension MathExtensions on double {
  double sin() => dart_math.sin(this);
  double cos() => dart_math.cos(this);
  double asin() => dart_math.asin(this);
  double sqrt() => dart_math.sqrt(this);
}
