name: aapatmitra
description: A disaster response mobile application using Flutter and Supabase.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6
  http: ^1.1.0
  flutter_riverpod: ^2.4.9
  hooks_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3
  supabase_flutter: ^2.3.1
  geolocator: ^10.1.0
  google_maps_flutter: ^2.5.0
  flutter_background_service: ^5.0.5
  audioplayers: ^5.2.1
  permission_handler: ^11.1.0
  connectivity_plus: ^5.0.2
  google_fonts: ^6.1.0
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.10
  flutter_local_notifications: ^16.3.0
  workmanager: ^0.5.2
  shared_preferences: ^2.2.2
  geocoding: ^2.1.1
  vibration: ^1.8.4
  wakelock_plus: ^1.1.3
  flutter_hooks: ^0.20.4

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  riverpod_generator: ^2.3.9
  build_runner: ^2.4.8

flutter:
  uses-material-design: true

  assets:
    - assets/sounds/
    - assets/images/