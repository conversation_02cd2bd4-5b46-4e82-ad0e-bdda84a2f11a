import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../services/government_service.dart';
import '../../models/government_alert.dart';

class AlertManagementScreen extends ConsumerWidget {
  const AlertManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Alert Management'),
      ),
      body: const Center(
        child: Text('Alert Management Screen'),
      ),
    );
  }
}
