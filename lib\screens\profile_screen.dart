import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/user_provider.dart';
import 'firebase_test_screen.dart';

class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(userProvider);
    return Scaffold(
      backgroundColor: const Color(0xFFF5ECE7),
      body: Safe<PERSON><PERSON>(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 16),
              // Avatar
              CircleAvatar(
                radius: 48,
                backgroundColor: const Color(0xFFD9BBA0),
                child: Icon(Icons.person, size: 48, color: Colors.brown.shade700),
              ),
              const SizedBox(height: 16),
              Text(user.name, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20)),
              Text(user.phone, style: const TextStyle(color: Colors.grey)),
              const SizedBox(height: 24),
              // Safety Status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text('Safety Status', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFD9BBA0),
                      foregroundColor: Colors.brown,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    onPressed: () {},
                    child: const Text('Update'),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Align(
                alignment: Alignment.centerLeft,
                child: Chip(
                  label: Text('Current Status: ${user.status[0].toUpperCase()}${user.status.substring(1)}'),
                  backgroundColor: user.status == 'safe' ? Colors.greenAccent : Colors.redAccent,
                ),
              ),
              const SizedBox(height: 24),
              // Emergency Contacts
              const Align(
                alignment: Alignment.centerLeft,
                child: Text('Emergency Contacts', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
              ),
              const SizedBox(height: 8),
              ...user.emergencyContacts.map((contact) => Card(
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                color: Colors.white,
                child: ListTile(
                  leading: const Icon(Icons.contact_phone, color: Color(0xFFD9BBA0)),
                  title: Text(contact),
                  trailing: IconButton(
                    icon: const Icon(Icons.edit, color: Colors.brown),
                    onPressed: () {},
                  ),
                ),
              )),
              const Spacer(),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFD9BBA0),
                    foregroundColor: Colors.brown,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    textStyle: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  onPressed: () {},
                  child: const Text('Add Contact'),
                ),
              ),
              const SizedBox(height: 8),
              // Firebase Test Button (for development)
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    textStyle: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (context) => const FirebaseTestScreen()),
                    );
                  },
                  icon: const Icon(Icons.cloud_queue),
                  label: const Text('Get FCM Token'),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }
}