import 'dart:async';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:vibration/vibration.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'firebase_service.dart';

class AlertService {
  static final SupabaseClient _client = Supabase.instance.client;
  static final FlutterLocalNotificationsPlugin _localNotifications = FlutterLocalNotificationsPlugin();
  static final FirebaseMessaging _firebaseMessaging = FirebaseService.messaging;
  static StreamSubscription<RemoteMessage>? _messageSubscription;
  static BuildContext? _context;

  /// Initialize alert service
  static Future<void> initialize(BuildContext context) async {
    _context = context;

    await _initializeLocalNotifications();
    if (!kIsWeb) {
      await _initializeFirebaseMessaging();
    }
    await _subscribeToAlerts();
  }

  /// Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    const AndroidInitializationSettings androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const DarwinInitializationSettings iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings settings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      settings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Request permissions
    await _localNotifications
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.requestNotificationsPermission();
  }

  /// Initialize Firebase messaging
  static Future<void> _initializeFirebaseMessaging() async {
    try {
      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // Handle background messages
      FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

    } catch (e) {
      print('Error initializing Firebase messaging: $e');
    }
  }

  /// Subscribe to real-time alerts from Supabase
  static Future<void> _subscribeToAlerts() async {
    try {
      _client
          .from('government_alerts')
          .stream(primaryKey: ['id'])
          .eq('is_active', true)
          .listen((data) {
            for (var alert in data) {
              _handleRealtimeAlert(alert);
            }
          });
    } catch (e) {
      print('Error subscribing to alerts: $e');
    }
  }

  /// Handle real-time alert from Supabase
  static Future<void> _handleRealtimeAlert(Map<String, dynamic> alert) async {
    final severity = alert['severity'] as String;

    if (severity == 'critical' || severity == 'urgent') {
      await showCriticalAlert(
        title: alert['title'],
        message: alert['message'],
        alertType: alert['alert_type'],
      );
    } else {
      await showLocalNotification(
        title: alert['title'],
        body: alert['message'],
        priority: severity == 'warning' ? Priority.high : Priority.defaultPriority,
      );
    }
  }

  /// Show critical alert (full screen, persistent)
  static Future<void> showCriticalAlert({
    required String title,
    required String message,
    required String alertType,
  }) async {
    try {
      // Show full screen notification
      await showLocalNotification(
        title: title,
        body: message,
        priority: Priority.max,
        importance: Importance.max,
        fullScreenIntent: true,
      );

      // Vibrate
      await Vibration.vibrate(pattern: [500, 1000, 500, 1000]);

      // Show in-app alert if context is available
      if (_context != null) {
        _showInAppAlert(title, message, alertType);
      }

    } catch (e) {
      print('Error showing critical alert: $e');
    }
  }

  /// Show local notification
  static Future<void> showLocalNotification({
    required String title,
    required String body,
    Priority priority = Priority.defaultPriority,
    Importance importance = Importance.defaultImportance,
    bool fullScreenIntent = false,
  }) async {
    try {
      final AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
        'emergency_channel',
        'Emergency Alerts',
        channelDescription: 'Critical emergency notifications',
        importance: importance,
        priority: priority,
        fullScreenIntent: fullScreenIntent,
        category: AndroidNotificationCategory.alarm,
        visibility: NotificationVisibility.public,
        enableVibration: true,
        playSound: true,
        sound: const RawResourceAndroidNotificationSound('emergency_alarm'),
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: 'emergency_alarm.wav',
        interruptionLevel: InterruptionLevel.critical,
      );

      final NotificationDetails details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch ~/ 1000,
        title,
        body,
        details,
      );

    } catch (e) {
      print('Error showing local notification: $e');
    }
  }

  /// Send emergency alert to all users in area
  static Future<void> sendEmergencyAlert({
    required String title,
    required String message,
    required String disasterType,
    double? latitude,
    double? longitude,
    double? radiusKm,
  }) async {
    try {
      // Save alert to database
      await _client.from('government_alerts').insert({
        'title': title,
        'message': message,
        'alert_type': 'emergency',
        'severity': 'critical',
        'target_area': latitude != null && longitude != null
            ? 'POINT($longitude $latitude)'
            : null,
        'expires_at': DateTime.now().add(const Duration(hours: 24)).toIso8601String(),
      });

      // Send push notification to all users
      await _sendPushNotificationToAll(title, message);

      // Show local notification
      await showCriticalAlert(
        title: title,
        message: message,
        alertType: disasterType,
      );

    } catch (e) {
      print('Error sending emergency alert: $e');
    }
  }

  /// Send push notification to all users
  static Future<void> _sendPushNotificationToAll(String title, String message) async {
    try {
      // This would typically be done through a cloud function
      // For now, we'll just trigger local notifications
      await showLocalNotification(
        title: title,
        body: message,
        priority: Priority.max,
        importance: Importance.max,
      );
    } catch (e) {
      print('Error sending push notification: $e');
    }
  }

  /// Show in-app alert dialog
  static void _showInAppAlert(String title, String message, String alertType) {
    if (_context == null) return;

    showDialog(
      context: _context!,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              _getAlertIcon(alertType),
              color: Colors.red,
              size: 32,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  color: Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('ACKNOWLEDGE'),
          ),
        ],
      ),
    );
  }

  /// Get appropriate icon for alert type
  static IconData _getAlertIcon(String alertType) {
    switch (alertType.toLowerCase()) {
      case 'earthquake':
        return Icons.terrain;
      case 'flood':
        return Icons.water;
      case 'fire':
        return Icons.local_fire_department;
      case 'evacuation':
        return Icons.exit_to_app;
      default:
        return Icons.warning;
    }
  }

  /// Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    // Handle notification tap - navigate to appropriate screen
    print('Notification tapped: ${response.payload}');
  }

  /// Handle foreground message
  static Future<void> _handleForegroundMessage(RemoteMessage message) async {
    print('Handling foreground message: ${message.messageId}');
    
    final notification = message.notification;
    final data = message.data;
    
    if (notification != null) {
      await showLocalNotification(
        title: notification.title ?? 'New Alert',
        body: notification.body ?? '',
        priority: Priority.high,
      );
    }

    // Handle data payload if needed
    if (data.containsKey('alert_type')) {
      final alertType = data['alert_type'];
      if (alertType == 'critical' || alertType == 'urgent') {
        await showCriticalAlert(
          title: notification?.title ?? 'Critical Alert',
          message: notification?.body ?? '',
          alertType: alertType,
        );
      }
    }
  }

  /// Handle background message
  @pragma('vm:entry-point')
  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    print('Handling background message: ${message.messageId}');
    
    final notification = message.notification;
    if (notification != null) {
      await showLocalNotification(
        title: notification.title ?? 'New Alert',
        body: notification.body ?? '',
        priority: Priority.high,
      );
    }
  }

  /// Save FCM token to database
  static Future<void> _saveFCMToken(String token) async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) return;

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('fcm_token', token);

      // Update user record with FCM token
      await _client
          .from('users')
          .update({'fcm_token': token})
          .eq('auth_user_id', user.id);

    } catch (e) {
      print('Error saving FCM token: $e');
    }
  }

  /// Get all active alerts
  static Future<List<Map<String, dynamic>>> getActiveAlerts() async {
    try {
      final response = await _client
          .from('government_alerts')
          .select('*')
          .eq('is_active', true)
          .gte('expires_at', DateTime.now().toIso8601String())
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('Error getting active alerts: $e');
      return [];
    }
  }

  /// Mark alert as read
  static Future<void> markAlertAsRead(String alertId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final readAlerts = prefs.getStringList('read_alerts') ?? [];

      if (!readAlerts.contains(alertId)) {
        readAlerts.add(alertId);
        await prefs.setStringList('read_alerts', readAlerts);
      }
    } catch (e) {
      print('Error marking alert as read: $e');
    }
  }

  /// Dispose
  static void dispose() {
    _messageSubscription?.cancel();
    _context = null;
  }
}
