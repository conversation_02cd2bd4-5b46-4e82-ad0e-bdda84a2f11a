-- URGENT: Execute this SQL in Supabase to fix infinite recursion
-- This will fix the main database issues

-- 1. Temporarily disable <PERSON><PERSON> to fix the policies
ALTER TABLE public.users DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.government_users DISABLE ROW LEVEL SECURITY;

-- 2. Drop all problematic policies
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.users;
DROP POLICY IF EXISTS "Government users can view all users during disasters" ON public.users;
DROP POLICY IF EXISTS "Government users can view all users" ON public.users;

-- 3. Create simple, non-recursive policies
CREATE POLICY "users_select_own" ON public.users
  FOR SELECT USING (auth.uid() = auth_user_id);

CREATE POLICY "users_insert_own" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = auth_user_id);

CREATE POLICY "users_update_own" ON public.users
  FOR UPDATE USING (auth.uid() = auth_user_id);

-- 4. Simple government users policy
CREATE POLICY "government_users_select" ON public.government_users
  FOR ALL USING (true); -- Temporarily allow all access

-- 5. Re-enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.government_users ENABLE ROW LEVEL SECURITY;

-- 6. Create a test government user for current session
-- Replace 'YOUR_AUTH_USER_ID' with your actual auth user ID
-- You can find this in Supabase Auth > Users section

-- First, insert into users table if not exists
INSERT INTO public.users (auth_user_id, phone, name, emergency_contacts)
VALUES (
  auth.uid(),
  '+91-9876543210',
  'Test Admin User',
  '["+91-9123456789", "+91-9876543211"]'
) ON CONFLICT (auth_user_id) DO NOTHING;

-- Then create government user
INSERT INTO public.government_users (user_id, department, clearance_level, region_access, can_activate_disasters)
SELECT 
  u.id,
  'admin',
  5,
  ARRAY['Delhi', 'NCR', 'All'],
  true
FROM public.users u 
WHERE u.auth_user_id = auth.uid()
ON CONFLICT DO NOTHING;
