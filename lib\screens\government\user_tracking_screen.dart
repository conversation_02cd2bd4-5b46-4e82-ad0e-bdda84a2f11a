import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../services/government_service.dart';
import '../../models/user.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class UserTrackingScreen extends ConsumerWidget {
  const UserTrackingScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Tracking'),
      ),
      body: const Center(
        child: Text('User Tracking Screen'),
      ),
    );
  }
}
