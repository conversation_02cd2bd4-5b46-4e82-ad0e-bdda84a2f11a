-- Row Level Security Policies for AapatMitra
-- Execute these commands after creating the tables

-- Enable RLS on all tables
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.disasters ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.live_user_locations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.government_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rescue_camps ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.hazard_zones ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.safe_routes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.emergency_facilities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.government_alerts ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON public.users
  FOR SELECT USING (auth.uid() = auth_user_id);

CREATE POLICY "Users can update their own profile" ON public.users
  FOR UPDATE USING (auth.uid() = auth_user_id);

CREATE POLICY "Users can insert their own profile" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = auth_user_id);

-- Government users can view all user profiles during disasters
CREATE POLICY "Government users can view all users during disasters" ON public.users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      JOIN public.users u ON gu.user_id = u.id 
      WHERE u.auth_user_id = auth.uid()
    )
  );

-- Disasters table policies
CREATE POLICY "Everyone can view active disasters" ON public.disasters
  FOR SELECT USING (is_active = true);

CREATE POLICY "Government users can manage disasters" ON public.disasters
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      JOIN public.users u ON gu.user_id = u.id 
      WHERE u.auth_user_id = auth.uid() AND gu.can_activate_disasters = true
    )
  );

-- Live user locations policies
CREATE POLICY "Users can manage their own location" ON public.live_user_locations
  FOR ALL USING (
    user_id IN (
      SELECT id FROM public.users WHERE auth_user_id = auth.uid()
    )
  );

CREATE POLICY "Government users can view all locations during disasters" ON public.live_user_locations
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      JOIN public.users u ON gu.user_id = u.id 
      WHERE u.auth_user_id = auth.uid()
    )
  );

-- Rescue camps policies
CREATE POLICY "Everyone can view active rescue camps" ON public.rescue_camps
  FOR SELECT USING (is_active = true);

CREATE POLICY "Government users can manage rescue camps" ON public.rescue_camps
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      JOIN public.users u ON gu.user_id = u.id 
      WHERE u.auth_user_id = auth.uid()
    )
  );

-- Hazard zones policies
CREATE POLICY "Everyone can view active hazard zones" ON public.hazard_zones
  FOR SELECT USING (is_active = true);

CREATE POLICY "Government users can manage hazard zones" ON public.hazard_zones
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      JOIN public.users u ON gu.user_id = u.id 
      WHERE u.auth_user_id = auth.uid()
    )
  );

-- Safe routes policies
CREATE POLICY "Everyone can view safe routes" ON public.safe_routes
  FOR SELECT USING (true);

CREATE POLICY "Government users can manage safe routes" ON public.safe_routes
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      JOIN public.users u ON gu.user_id = u.id 
      WHERE u.auth_user_id = auth.uid()
    )
  );

-- Emergency facilities policies
CREATE POLICY "Everyone can view emergency facilities" ON public.emergency_facilities
  FOR SELECT USING (true);

CREATE POLICY "Government users can manage emergency facilities" ON public.emergency_facilities
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      JOIN public.users u ON gu.user_id = u.id 
      WHERE u.auth_user_id = auth.uid()
    )
  );

-- Government alerts policies
CREATE POLICY "Everyone can view active alerts" ON public.government_alerts
  FOR SELECT USING (is_active = true);

CREATE POLICY "Government users can manage alerts" ON public.government_alerts
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.government_users gu 
      JOIN public.users u ON gu.user_id = u.id 
      WHERE u.auth_user_id = auth.uid()
    )
  );

-- Create indexes for better performance
CREATE INDEX idx_users_auth_user_id ON public.users(auth_user_id);
CREATE INDEX idx_disasters_active ON public.disasters(is_active);
CREATE INDEX idx_live_locations_user_disaster ON public.live_user_locations(user_id, disaster_id);
CREATE INDEX idx_rescue_camps_active ON public.rescue_camps(is_active);
CREATE INDEX idx_hazard_zones_active ON public.hazard_zones(is_active);
CREATE INDEX idx_government_alerts_active ON public.government_alerts(is_active);

-- Create functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add triggers for automatic timestamp updates
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_disasters_updated_at BEFORE UPDATE ON public.disasters
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
