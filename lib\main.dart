import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'config/supabase_config.dart';
import 'screens/home_screen.dart';
import 'screens/camps_screen.dart';
import 'screens/alerts_screen.dart';
import 'screens/profile_screen.dart';
import 'screens/auth/login_screen.dart';
import 'services/auth_service.dart';
import 'services/location_service.dart';
import 'services/disaster_detection_service.dart';
import 'services/alarm_service.dart';
import 'services/alert_service.dart';
import 'services/test_data_service.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Supabase
  await Supabase.initialize(
    url: SupabaseConfig.projectUrl,
    anonKey: SupabaseConfig.anonKey,
    authOptions: const FlutterAuthClientOptions(
      authFlowType: AuthFlowType.pkce,
    ),
  );

  // Initialize services
  await AlarmService.initialize();

  runApp(const ProviderScope(child: AapatMitraApp()));
}

class AapatMitraApp extends StatelessWidget {
  const AapatMitraApp({super.key});

  @override
  Widget build(BuildContext context) {
    final baseTextTheme = Theme.of(context).textTheme;
    return MaterialApp(
      title: 'AapatMitra',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFFD9BBA0),
          primary: const Color(0xFFD9BBA0),
          secondary: Colors.brown,
          onPrimary: Colors.brown.shade800,
          background: const Color(0xFFF5ECE7),
          surface: Colors.white,
        ),
        scaffoldBackgroundColor: const Color(0xFFF5ECE7),
        appBarTheme: AppBarTheme(
          backgroundColor: const Color(0xFFF5ECE7),
          foregroundColor: Colors.brown.shade800,
          elevation: 0,
          iconTheme: IconThemeData(color: Colors.brown.shade800),
          titleTextStyle: GoogleFonts.nunito(
              color: Colors.brown.shade800,
              fontSize: 20,
              fontWeight: FontWeight.bold),
        ),
        textTheme: GoogleFonts.nunitoTextTheme(baseTextTheme).apply(
          bodyColor: Colors.brown.shade800,
          displayColor: Colors.brown.shade900,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFD9BBA0),
            foregroundColor: Colors.brown.shade800,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 24),
            textStyle: GoogleFonts.nunito(fontWeight: FontWeight.bold, fontSize: 16),
          ),
        ),
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            foregroundColor: Colors.brown.shade700,
            textStyle: GoogleFonts.nunito(fontWeight: FontWeight.w600),
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide(color: Colors.brown.shade200)),
          focusedBorder: OutlineInputBorder(borderRadius: BorderRadius.circular(12), borderSide: BorderSide(color: Colors.brown.shade600, width: 2)),
          labelStyle: GoogleFonts.nunito(color: Colors.brown.shade700),
          hintStyle: GoogleFonts.nunito(color: Colors.grey.shade600),
        ),
        cardTheme: CardTheme(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          color: Colors.white,
          margin: const EdgeInsets.symmetric(vertical: 8.0),
        ),
        chipTheme: ChipThemeData(
          backgroundColor: const Color(0xFFE2D6CE),
          labelStyle: GoogleFonts.nunito(color: Colors.brown.shade800, fontWeight: FontWeight.w600),
          selectedColor: const Color(0xFFD9BBA0),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
        bottomNavigationBarTheme: BottomNavigationBarThemeData(
          backgroundColor: Colors.white,
          selectedItemColor: Colors.brown.shade800,
          unselectedItemColor: Colors.brown.withOpacity(0.7),
          selectedLabelStyle: GoogleFonts.nunito(fontWeight: FontWeight.bold),
          unselectedLabelStyle: GoogleFonts.nunito(),
          type: BottomNavigationBarType.fixed,
        ),
        useMaterial3: true,
      ),
      home: StreamBuilder<AuthState>(
        stream: AuthService.client.auth.onAuthStateChange,
        builder: (context, snapshot) {
          final session = snapshot.data?.session;
          if (session != null) {
            return const MainNavigation();
          }
          return const LoginScreen();
        },
      ),
    );
  }
}

class MainNavigation extends StatefulWidget {
  const MainNavigation({super.key});

  @override
  State<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends State<MainNavigation> {
  int _selectedIndex = 0;
  static final List<Widget> _screens = [
    const HomeScreen(),
    const CampsScreen(),
    const AlertsScreen(),
    const ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      // Initialize alert service with context
      await AlertService.initialize(context);

      // Request location permissions
      await LocationService.requestLocationPermission();

      // Temporarily disable monitoring to fix database issues
      // await DisasterDetectionService.startMonitoring();

      // Check if alarm should be restarted
      await AlarmService.checkAlarmState();

      // Initialize test data for demonstration
      await TestDataService.initializeTestData();

    } catch (e) {
      print('Error initializing services: $e');
    }
  }

  @override
  void dispose() {
    // Clean up services
    DisasterDetectionService.stopMonitoring();
    AlarmService.dispose();
    AlertService.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.home_filled), label: 'Home'),
          BottomNavigationBarItem(icon: Icon(Icons.cabin), label: 'Camps'),
          BottomNavigationBarItem(icon: Icon(Icons.warning), label: 'Alerts'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }
}