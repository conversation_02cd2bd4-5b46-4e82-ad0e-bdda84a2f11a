import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'package:aapatmitra/screens/home_screen.dart';
import 'package:aapatmitra/screens/camps_screen.dart';
import 'package:aapatmitra/screens/alerts_screen.dart';
import 'package:aapatmitra/screens/profile_screen.dart';
import 'package:aapatmitra/screens/auth/login_screen.dart';
import 'package:aapatmitra/services/auth_service.dart';
import 'package:aapatmitra/services/location_service.dart';
import 'package:aapatmitra/services/disaster_detection_service.dart';
import 'package:aapatmitra/services/alarm_service.dart';
import 'package:aapatmitra/services/alert_service.dart';
import 'package:aapatmitra/services/test_data_service.dart';
import 'package:aapatmitra/services/firebase_service.dart';
import 'package:aapatmitra/config/firebase_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await Supabase.initialize(
    url: 'YOUR_SUPABASE_URL',
    anonKey: 'YOUR_SUPABASE_ANON_KEY',
  );

  await Firebase.initializeApp(
    options: FirebaseConfig.android,
  );

  await FirebaseService.initialize();

  runApp(
    const ProviderScope(
      child: AapatMitraApp(),
    ),
  );
}

class AapatMitraApp extends StatelessWidget {
  const AapatMitraApp({super.key});

  @override
  Widget build(BuildContext context) {
    final baseTextTheme = Theme.of(context).textTheme;

    return MaterialApp(
      title: 'Aapat Mitra',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.brown,
          primary: Colors.brown,
          secondary: Colors.amber,
        ),
        useMaterial3: true,
        textTheme: GoogleFonts.nunitoTextTheme(baseTextTheme).copyWith(
          titleLarge: GoogleFonts.nunito(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.brown.shade900,
          ),
        ).apply(
          bodyColor: Colors.brown.shade900,
          displayColor: Colors.brown.shade900,
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.brown.shade700,
            foregroundColor: Colors.white,
            textStyle: GoogleFonts.nunito(fontWeight: FontWeight.bold, fontSize: 16),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        ),
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.brown.shade700,
            textStyle: GoogleFonts.nunito(fontWeight: FontWeight.w600),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          labelStyle: GoogleFonts.nunito(color: Colors.brown.shade700),
          hintStyle: GoogleFonts.nunito(color: Colors.grey.shade600),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.brown.shade200),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.brown.shade200),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: Colors.brown.shade700, width: 2),
          ),
          floatingLabelStyle: const TextStyle(fontSize: 20),
        ),
        bottomNavigationBarTheme: BottomNavigationBarThemeData(
          backgroundColor: Colors.white,
          selectedItemColor: Colors.brown.shade700,
          unselectedItemColor: Colors.brown.shade300,
          selectedLabelStyle: GoogleFonts.nunito(fontWeight: FontWeight.bold),
          unselectedLabelStyle: GoogleFonts.nunito(),
        ),
      ),
      home: StreamBuilder<AuthState>(
        stream: Supabase.instance.client.auth.onAuthStateChange,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          }

          if (!snapshot.hasData || snapshot.data == null) {
            return const LoginScreen();
          }

          return const MainNavigation();
        },
      ),
    );
  }
}

class MainNavigation extends ConsumerStatefulWidget {
  const MainNavigation({super.key});

  @override
  ConsumerState<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends ConsumerState<MainNavigation> {
  int _selectedIndex = 0;

  final List<Widget> _screens = [
    const HomeScreen(),
    const CampsScreen(),
    const AlertsScreen(),
    const ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    try {
      // Initialize alert service with context
      await AlertService.initialize(context);

      // Request location permissions
      await LocationService.initialize();

      // Get and display FCM token for testing
      await FirebaseService.displayTokenForTesting();

      // Temporarily disable monitoring to fix database issues
      // await DisasterDetectionService.startDisasterMonitoring();

      // Check if alarm should be restarted
      await AlarmService.checkAndRestoreAlarmState();

      // Initialize test data for demonstration
      await TestDataService.initializeTestData();

    } catch (e) {
      print('Error initializing services: $e');
    }
  }

  @override
  void dispose() {
    // Clean up services
    DisasterDetectionService.stopDisasterMonitoring();
    AlarmService.stopEmergencyAlarm();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: 'Home',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.cabin_outlined),
            activeIcon: Icon(Icons.cabin),
            label: 'Camps',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.notifications_outlined),
            activeIcon: Icon(Icons.notifications),
            label: 'Alerts',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            activeIcon: Icon(Icons.person),
            label: 'Profile',
          ),
        ],
      ),
    );
  }
}