import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/government_service.dart';

class DisasterManagementScreen extends ConsumerStatefulWidget {
  const DisasterManagementScreen({super.key});

  @override
  ConsumerState<DisasterManagementScreen> createState() => _DisasterManagementScreenState();
}

class _DisasterManagementScreenState extends ConsumerState<DisasterManagementScreen> {
  List<Map<String, dynamic>> disasters = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDisasters();
  }

  Future<void> _loadDisasters() async {
    setState(() => isLoading = true);
    try {
      final loadedDisasters = await GovernmentService.getActiveDisasters();
      setState(() {
        disasters = loadedDisasters;
        isLoading = false;
      });
    } catch (e) {
      setState(() => isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading disasters: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Disaster Management'),
        backgroundColor: Colors.red.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showActivateDisasterDialog,
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadDisasters,
              child: disasters.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.check_circle, size: 64, color: Colors.green),
                          SizedBox(height: 16),
                          Text(
                            'No Active Disasters',
                            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                          ),
                          Text('All clear in monitored areas'),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: disasters.length,
                      itemBuilder: (context, index) {
                        final disaster = disasters[index];
                        return _buildDisasterCard(disaster);
                      },
                    ),
            ),
    );
  }

  Widget _buildDisasterCard(Map<String, dynamic> disaster) {
    final type = disaster['type'] as String;
    final magnitude = disaster['magnitude'] as double?;
    final description = disaster['description'] as String?;
    final createdAt = DateTime.parse(disaster['created_at'] as String);
    final source = disaster['activation_source'] as String;

    IconData icon;
    Color color;
    switch (type) {
      case 'earthquake':
        icon = Icons.terrain;
        color = Colors.brown;
        break;
      case 'flood':
        icon = Icons.water;
        color = Colors.blue;
        break;
      case 'fire':
        icon = Icons.local_fire_department;
        color = Colors.red;
        break;
      default:
        icon = Icons.warning;
        color = Colors.orange;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: color.withOpacity(0.1),
                  child: Icon(icon, color: color),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        type.toUpperCase(),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: color,
                        ),
                      ),
                      if (magnitude != null)
                        Text(
                          'Magnitude: $magnitude',
                          style: TextStyle(color: Colors.grey.shade600),
                        ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: source == 'automatic' ? Colors.orange : Colors.blue,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    source.toUpperCase(),
                    style: const TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            if (description != null) ...[
              const SizedBox(height: 12),
              Text(
                description,
                style: TextStyle(color: Colors.grey.shade700),
              ),
            ],
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Colors.grey.shade600),
                const SizedBox(width: 4),
                Text(
                  'Activated: ${_formatDateTime(createdAt)}',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showDisasterDetails(disaster),
                    icon: const Icon(Icons.info, size: 16),
                    label: const Text('View Details'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _deactivateDisaster(disaster),
                    icon: const Icon(Icons.stop, size: 16),
                    label: const Text('Deactivate'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showActivateDisasterDialog() {
    final typeController = TextEditingController();
    final descriptionController = TextEditingController();
    final latitudeController = TextEditingController();
    final longitudeController = TextEditingController();
    final magnitudeController = TextEditingController();
    final radiusController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Activate Disaster'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: typeController,
                decoration: const InputDecoration(
                  labelText: 'Disaster Type (e.g., earthquake, flood, fire)',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: descriptionController,
                maxLines: 2,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: latitudeController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'Latitude',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: longitudeController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'Longitude',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: magnitudeController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'Magnitude (optional)',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: radiusController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'Radius (km)',
                        border: OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (typeController.text.isNotEmpty &&
                  descriptionController.text.isNotEmpty &&
                  latitudeController.text.isNotEmpty &&
                  longitudeController.text.isNotEmpty) {
                
                Navigator.pop(context);
                
                final success = await GovernmentService.activateDisaster(
                  type: typeController.text,
                  latitude: double.parse(latitudeController.text),
                  longitude: double.parse(longitudeController.text),
                  description: descriptionController.text,
                  magnitude: double.tryParse(magnitudeController.text),
                  radiusKm: int.tryParse(radiusController.text) ?? 50,
                );

                if (success) {
                  _loadDisasters();
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Disaster activated successfully')),
                    );
                  }
                } else {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Failed to activate disaster')),
                    );
                  }
                }
              }
            },
            child: const Text('Activate'),
          ),
        ],
      ),
    );
  }

  void _showDisasterDetails(Map<String, dynamic> disaster) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${disaster['type'].toString().toUpperCase()} Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('ID: ${disaster['id']}'),
            Text('Type: ${disaster['type']}'),
            if (disaster['magnitude'] != null)
              Text('Magnitude: ${disaster['magnitude']}'),
            Text('Radius: ${disaster['radius_km']} km'),
            Text('Source: ${disaster['activation_source']}'),
            Text('Created: ${_formatDateTime(DateTime.parse(disaster['created_at']))}'),
            if (disaster['description'] != null)
              Text('Description: ${disaster['description']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Future<void> _deactivateDisaster(Map<String, dynamic> disaster) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Deactivate Disaster'),
        content: Text('Are you sure you want to deactivate this ${disaster['type']} disaster?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Deactivate'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await GovernmentService.deactivateDisaster(disaster['id']);
      
      if (success) {
        _loadDisasters();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Disaster deactivated successfully')),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to deactivate disaster')),
          );
        }
      }
    }
  }
}
