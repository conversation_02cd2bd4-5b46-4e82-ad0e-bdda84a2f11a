class HazardZone {
  final String id;
  final String hazardType; // 'flood', 'fire', etc.
  final int severityLevel; // 1-5
  final String description;
  final bool isActive;

  HazardZone({
    required this.id,
    required this.hazardType,
    required this.severityLevel,
    required this.description,
    required this.isActive,
  });

  // Mock hazard zones
  static List<HazardZone> mockList() => [
    HazardZone(id: '1', hazardType: 'flood', severityLevel: 4, description: 'Flooded area near river', isActive: true),
    HazardZone(id: '2', hazardType: 'fire', severityLevel: 3, description: 'Forest fire in Zone B', isActive: false),
  ];
} 