import 'dart:async';
import 'package:audioplayers/audioplayers.dart';
import 'package:vibration/vibration.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

class AlarmService {
  static AudioPlayer? _audioPlayer;
  static Timer? _alarmTimer;
  static Timer? _vibrationTimer;
  static bool _isAlarmActive = false;
  static bool _userStoppedAlarm = false;
  static int _alarmVolume = 100;

  // Alarm patterns
  static const List<int> _emergencyVibrationPattern = [500, 1000, 500, 1000];
  static const Duration _alarmInterval = Duration(seconds: 2);

  /// Initialize alarm service
  static Future<void> initialize() async {
    _audioPlayer = AudioPlayer();
    await _loadAlarmSettings();
  }

  /// Start emergency alarm (cannot be stopped programmatically)
  static Future<void> startEmergencyAlarm() async {
    if (_isAlarmActive) return;

    try {
      _isAlarmActive = true;
      _userStoppedAlarm = false;

      // Keep screen awake during emergency
      await WakelockPlus.enable();

      // Start audio alarm
      await _startAudioAlarm();

      // Start vibration pattern
      await _startVibrationAlarm();

      // Save alarm state
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('alarm_active', true);
      await prefs.setString('alarm_start_time', DateTime.now().toIso8601String());

      print('Emergency alarm started');

    } catch (e) {
      print('Error starting emergency alarm: $e');
    }
  }

  /// Stop alarm (only when user manually stops it)
  static Future<void> stopAlarmByUser() async {
    if (!_isAlarmActive) return;

    try {
      _userStoppedAlarm = true;
      await _stopAllAlarms();

      // Save user action
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('user_stopped_alarm', true);
      await prefs.setString('alarm_stop_time', DateTime.now().toIso8601String());

      print('Alarm stopped by user');

    } catch (e) {
      print('Error stopping alarm: $e');
    }
  }

  /// Internal method to stop all alarm components
  static Future<void> _stopAllAlarms() async {
    _isAlarmActive = false;

    // Stop audio
    await _audioPlayer?.stop();
    _alarmTimer?.cancel();
    _alarmTimer = null;

    // Stop vibration
    _vibrationTimer?.cancel();
    _vibrationTimer = null;
    if (!kIsWeb) {
      try {
        await Vibration.cancel();
      } catch (e) {
        // Ignore vibration errors on web
      }
    }

    // Release wakelock
    await WakelockPlus.disable();

    // Save state
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('alarm_active', false);
  }

  /// Start audio alarm with continuous loop
  static Future<void> _startAudioAlarm() async {
    try {
      // Use system alarm sound or custom emergency sound
      await _audioPlayer?.setVolume(_alarmVolume / 100.0);

      // Create repeating alarm timer
      _alarmTimer = Timer.periodic(_alarmInterval, (timer) async {
        if (_isAlarmActive && !_userStoppedAlarm) {
          try {
            // Play emergency sound
            await _audioPlayer?.play(AssetSource('sounds/emergency_alarm.mp3'));

            // Also trigger system notification sound
            await SystemSound.play(SystemSoundType.alert);

          } catch (e) {
            // Fallback to system sound only
            await SystemSound.play(SystemSoundType.alert);
          }
        } else {
          timer.cancel();
        }
      });

    } catch (e) {
      print('Error starting audio alarm: $e');
      // Fallback to system sound
      await SystemSound.play(SystemSoundType.alert);
    }
  }

  /// Start vibration alarm
  static Future<void> _startVibrationAlarm() async {
    try {
      // Skip vibration on web platform
      if (kIsWeb) return;

      bool? hasVibrator = await Vibration.hasVibrator();
      if (hasVibrator != true) return;

      _vibrationTimer = Timer.periodic(
        const Duration(seconds: 3),
        (timer) async {
          if (_isAlarmActive && !_userStoppedAlarm) {
            await Vibration.vibrate(pattern: _emergencyVibrationPattern);
          } else {
            timer.cancel();
          }
        },
      );

    } catch (e) {
      print('Error starting vibration alarm: $e');
    }
  }

  /// Load alarm settings from preferences
  static Future<void> _loadAlarmSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _alarmVolume = prefs.getInt('alarm_volume') ?? 100;

      // Check if alarm was active before app restart
      bool wasAlarmActive = prefs.getBool('alarm_active') ?? false;
      if (wasAlarmActive && !_userStoppedAlarm) {
        // Restart alarm if it was active
        await startEmergencyAlarm();
      }

    } catch (e) {
      print('Error loading alarm settings: $e');
    }
  }

  /// Set alarm volume (0-100)
  static Future<void> setAlarmVolume(int volume) async {
    try {
      _alarmVolume = volume.clamp(0, 100);
      await _audioPlayer?.setVolume(_alarmVolume / 100.0);

      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('alarm_volume', _alarmVolume);

    } catch (e) {
      print('Error setting alarm volume: $e');
    }
  }

  /// Test alarm (for settings)
  static Future<void> testAlarm() async {
    try {
      await _audioPlayer?.play(AssetSource('sounds/emergency_alarm.mp3'));
      if (!kIsWeb) {
        await Vibration.vibrate(pattern: _emergencyVibrationPattern);
      }
    } catch (e) {
      await SystemSound.play(SystemSoundType.alert);
    }
  }

  /// Check if alarm should be restarted (for app lifecycle management)
  static Future<void> checkAlarmState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      bool alarmWasActive = prefs.getBool('alarm_active') ?? false;
      bool userStopped = prefs.getBool('user_stopped_alarm') ?? false;

      if (alarmWasActive && !userStopped && !_isAlarmActive) {
        await startEmergencyAlarm();
      }

    } catch (e) {
      print('Error checking alarm state: $e');
    }
  }

  /// Get alarm statistics
  static Future<Map<String, dynamic>> getAlarmStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      return {
        'is_active': _isAlarmActive,
        'user_stopped': _userStoppedAlarm,
        'volume': _alarmVolume,
        'start_time': prefs.getString('alarm_start_time'),
        'stop_time': prefs.getString('alarm_stop_time'),
      };

    } catch (e) {
      return {
        'is_active': _isAlarmActive,
        'user_stopped': _userStoppedAlarm,
        'volume': _alarmVolume,
      };
    }
  }

  /// Dispose resources
  static Future<void> dispose() async {
    await _stopAllAlarms();
    await _audioPlayer?.dispose();
    _audioPlayer = null;
  }

  // Getters
  static bool get isAlarmActive => _isAlarmActive;
  static bool get userStoppedAlarm => _userStoppedAlarm;
  static int get alarmVolume => _alarmVolume;
}
