import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

class FirebaseConfig {
  // Firebase configuration for AapatMitra
  // TODO: Replace with actual Firebase project configuration from Firebase Console
  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDummy-Android-Key-Replace-With-Real',
    appId: '1:123456789:android:abcdef123456',
    messagingSenderId: '123456789',
    projectId: 'aapatmitra-emergency',
    storageBucket: 'aapatmitra-emergency.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDummy-iOS-Key-Replace-With-Real',
    appId: '1:123456789:ios:abcdef123456',
    messagingSenderId: '123456789',
    projectId: 'aapatmitra-emergency',
    storageBucket: 'aapatmitra-emergency.appspot.com',
    iosBundleId: 'com.example.aapatmitra',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDummy-Web-Key-Replace-With-Real',
    appId: '1:123456789:web:abcdef123456',
    messagingSenderId: '123456789',
    projectId: 'aapatmitra-emergency',
    authDomain: 'aapatmitra-emergency.firebaseapp.com',
    storageBucket: 'aapatmitra-emergency.appspot.com',
  );

  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macOS - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }
}
