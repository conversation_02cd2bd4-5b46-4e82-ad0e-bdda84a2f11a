import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

class FirebaseConfig {
  // Firebase configuration for AapatMitra - Real project configuration
  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBYSj_g5FLP-oGh_17GOSk3cqo9sHMy7L4',
    appId: '1:501265298156:android:65c02699bd0d0ddbe66bc9',
    messagingSenderId: '501265298156',
    projectId: 'aapatmitra',
    storageBucket: 'aapatmitra.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBYSj_g5FLP-oGh_17GOSk3cqo9sHMy7L4',
    appId: '1:501265298156:ios:65c02699bd0d0ddbe66bc9',
    messagingSenderId: '501265298156',
    projectId: 'aapatmitra',
    storageBucket: 'aapatmitra.firebasestorage.app',
    iosBundleId: 'com.example.aapatmitra',
  );

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBYSj_g5FLP-oGh_17GOSk3cqo9sHMy7L4',
    appId: '1:501265298156:web:65c02699bd0d0ddbe66bc9',
    messagingSenderId: '501265298156',
    projectId: 'aapatmitra',
    authDomain: 'aapatmitra.firebaseapp.com',
    storageBucket: 'aapatmitra.firebasestorage.app',
    measurementId: 'G-FJL3NYMB5B',
  );

  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macOS - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }
}
