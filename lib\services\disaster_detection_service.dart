import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'location_service.dart';
import 'alert_service.dart';
import 'alarm_service.dart';

class DisasterDetectionService {
  static final SupabaseClient _client = Supabase.instance.client;
  static Timer? _monitoringTimer;
  static bool _isMonitoring = false;
  static const Duration _monitoringInterval = Duration(minutes: 5);
  static const double _earthquakeThreshold = 4.0; // Magnitude
  static const double _floodThreshold = 100.0; // mm of rainfall
  static const double _monitoringRadius = 100.0; // km
  static const String _usgsEarthquakeAPI = 'https://earthquake.usgs.gov/earthquakes/feed/v1.0/summary/2.5_day.geojson';
  static List<String> _activeDisasters = [];

  // API endpoints for disaster monitoring
  static const String _weatherAPI = 'https://api.openweathermap.org/data/2.5/weather'; // Requires API key
  
  static Future<void> initialize() async {
    await startDisasterMonitoring();
  }

  static Future<void> startDisasterMonitoring() async {
    if (_isMonitoring) return;
    _isMonitoring = true;

    try {
      // Initial check
      await _checkForDisasters();

      // Set up periodic monitoring
      _monitoringTimer = Timer.periodic(_monitoringInterval, (timer) async {
        await _checkForDisasters();
      });
    } catch (e) {
      print('Error starting disaster monitoring: $e');
    }
  }

  static Future<void> stopDisasterMonitoring() async {
    _isMonitoring = false;
    _monitoringTimer?.cancel();
    _monitoringTimer = null;
  }

  static Future<void> _checkForDisasters() async {
    try {
      await Future.wait([
        _checkForEarthquakes(),
        _checkForFloods(),
      ]);
    } catch (e) {
      print('Error checking for disasters: $e');
    }
  }

  static Future<void> _checkForEarthquakes() async {
    try {
      final response = await http.get(Uri.parse(_usgsEarthquakeAPI));
      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final features = data['features'] as List;

        for (var feature in features) {
          final properties = feature['properties'] as Map<String, dynamic>;
          final geometry = feature['geometry'] as Map<String, dynamic>;
          final coordinates = geometry['coordinates'] as List;

          final magnitude = (properties['mag'] as num).toDouble();
          final location = properties['place'] as String;
          final time = DateTime.fromMillisecondsSinceEpoch(properties['time'] as int);

          if (magnitude >= _earthquakeThreshold) {
            final earthquakeLocation = {
              'latitude': (coordinates[1] as num).toDouble(),
              'longitude': (coordinates[0] as num).toDouble(),
            };

            final userLocation = await _getCurrentUserLocation();
            if (userLocation != null) {
              final distance = Geolocator.distanceBetween(
                userLocation['latitude']!,
                userLocation['longitude']!,
                earthquakeLocation['latitude']!,
                earthquakeLocation['longitude']!,
              );

              if (distance <= _monitoringRadius * 1000) { // Convert km to meters
                await _saveDisasterEvent({
                  'type': 'earthquake',
                  'magnitude': magnitude,
                  'location': location,
                  'latitude': earthquakeLocation['latitude'],
                  'longitude': earthquakeLocation['longitude'],
                  'time': time.toIso8601String(),
                  'distance': distance,
                });
              }
            }
          }
        }
      }
    } catch (e) {
      print('Error checking for earthquakes: $e');
    }
  }

  static Future<void> _checkForFloods() async {
    try {
      // This is a placeholder for actual flood data API integration
      // You would typically integrate with a weather/flood monitoring API
      final userLocation = await _getCurrentUserLocation();
      if (userLocation != null) {
        // Example flood check logic
        final rainfall = await _getRainfallData(
          userLocation['latitude']!,
          userLocation['longitude']!,
        );

        if (rainfall > _floodThreshold) {
          await _saveDisasterEvent({
            'type': 'flood',
            'rainfall': rainfall,
            'latitude': userLocation['latitude'],
            'longitude': userLocation['longitude'],
            'time': DateTime.now().toIso8601String(),
          });
        }
      }
    } catch (e) {
      print('Error checking for floods: $e');
    }
  }

  static Future<Map<String, double>?> _getCurrentUserLocation() async {
    try {
      final position = await Geolocator.getCurrentPosition();
      return {
        'latitude': position.latitude,
        'longitude': position.longitude,
      };
    } catch (e) {
      print('Error getting user location: $e');
      return null;
    }
  }

  static Future<double> _getRainfallData(double latitude, double longitude) async {
    // This is a placeholder for actual rainfall data API integration
    // You would typically get this data from a weather API
    return 0.0;
  }

  static Future<void> _saveDisasterEvent(Map<String, dynamic> event) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return;

      // Save to Supabase
      await _client.from('disaster_events').insert({
        'user_id': userId,
        ...event,
      });

      // Save locally
      List<String> events = prefs.getStringList('disaster_events') ?? [];
      events.add(json.encode(event));
      await prefs.setStringList('disaster_events', events);

      // Trigger notifications or alarms based on the event
      await _handleDisasterEvent(event);
    } catch (e) {
      print('Error saving disaster event: $e');
    }
  }

  static Future<void> _handleDisasterEvent(Map<String, dynamic> event) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastNotificationTime = prefs.getString('last_disaster_notification');
      final now = DateTime.now();

      if (lastNotificationTime != null) {
        final lastTime = DateTime.parse(lastNotificationTime);
        if (now.difference(lastTime) < const Duration(minutes: 30)) {
          return; // Avoid too frequent notifications
        }
      }

      // Save notification time
      await prefs.setString('last_disaster_notification', now.toIso8601String());

      // Trigger appropriate response based on disaster type
      if (event['type'] == 'earthquake') {
        // Handle earthquake
        await _handleEarthquakeEvent(event);
      } else if (event['type'] == 'flood') {
        // Handle flood
        await _handleFloodEvent(event);
      }
    } catch (e) {
      print('Error handling disaster event: $e');
    }
  }

  static Future<void> _handleEarthquakeEvent(Map<String, dynamic> event) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_earthquake', json.encode(event));

      // Additional earthquake-specific handling
      // For example, trigger alarms, send notifications, etc.
    } catch (e) {
      print('Error handling earthquake event: $e');
    }
  }

  static Future<void> _handleFloodEvent(Map<String, dynamic> event) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('last_flood', json.encode(event));

      // Additional flood-specific handling
      // For example, trigger alarms, send notifications, etc.
    } catch (e) {
      print('Error handling flood event: $e');
    }
  }

  static Future<List<Map<String, dynamic>>> getRecentDisasters() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final events = prefs.getStringList('disaster_events') ?? [];
      return events.map((e) => json.decode(e) as Map<String, dynamic>).toList();
    } catch (e) {
      print('Error getting recent disasters: $e');
      return [];
    }
  }

  static Future<void> clearDisasterHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('disaster_events');
      await prefs.remove('last_earthquake');
      await prefs.remove('last_flood');
      await prefs.remove('last_disaster_notification');
    } catch (e) {
      print('Error clearing disaster history: $e');
    }
  }

  /// Check weather alerts (placeholder - requires API key)
  static Future<void> _checkWeatherAlerts(double userLat, double userLng) async {
    try {
      // TODO: Implement weather API integration
      // This would check for severe weather conditions like:
      // - Severe thunderstorms
      // - Tornadoes
      // - Flash floods
      // - Extreme temperatures
      
      // For now, we'll check our database for weather-related disasters
      await _checkDatabaseDisasters(userLat, userLng);
      
    } catch (e) {
      print('Error checking weather alerts: $e');
    }
  }

  /// Check government alerts from database
  static Future<void> _checkGovernmentAlerts(double userLat, double userLng) async {
    try {
      final response = await _client
          .from('government_alerts')
          .select('*')
          .eq('is_active', true)
          .gte('expires_at', DateTime.now().toIso8601String());

      for (var alert in response) {
        if (alert['severity'] == 'critical') {
          // Check if user is in target area
          final targetArea = alert['target_area'];
          if (targetArea != null) {
            // TODO: Implement polygon containment check
            // For now, activate for all critical alerts
            await AlertService.showCriticalAlert(
              title: alert['title'],
              message: alert['message'],
              alertType: alert['alert_type'],
            );
          }
        }
      }
    } catch (e) {
      print('Error checking government alerts: $e');
    }
  }

  /// Check database for existing disasters
  static Future<void> _checkDatabaseDisasters(double userLat, double userLng) async {
    try {
      final response = await _client
          .from('disasters')
          .select('*')
          .eq('is_active', true);

      for (var disaster in response) {
        final disasterId = disaster['id'];
        
        if (!_activeDisasters.contains(disasterId)) {
          _activeDisasters.add(disasterId);
          
          // Activate emergency protocols
          await _activateEmergencyProtocols(disaster);
        }
      }
    } catch (e) {
      print('Error checking database disasters: $e');
    }
  }

  /// Activate disaster in database
  static Future<void> _activateDisaster({
    required String type,
    required double latitude,
    required double longitude,
    required double magnitude,
    required String description,
    required String source,
    required int radiusKm,
  }) async {
    try {
      // Check if similar disaster already exists
      final existing = await _client
          .from('disasters')
          .select('id')
          .eq('type', type)
          .eq('is_active', true)
          .limit(1);

      if (existing.isNotEmpty) return; // Disaster already active

      // Create new disaster record
      final response = await _client
          .from('disasters')
          .insert({
            'type': type,
            'location': 'POINT($longitude $latitude)',
            'magnitude': magnitude,
            'radius_km': radiusKm,
            'is_active': true,
            'activation_source': source,
            'description': description,
          })
          .select()
          .single();

      final disasterId = response['id'];
      _activeDisasters.add(disasterId);

      // Activate emergency protocols
      await _activateEmergencyProtocols(response);

      print('Disaster activated: $type at ($latitude, $longitude)');
      
    } catch (e) {
      print('Error activating disaster: $e');
    }
  }

  /// Activate emergency protocols
  static Future<void> _activateEmergencyProtocols(Map<String, dynamic> disaster) async {
    try {
      // 1. Start persistent alarm
      await AlarmService.startEmergencyAlarm();

      // 2. Start emergency location tracking
      await LocationService.startEmergencyTracking();

      // 3. Send emergency alert
      await AlertService.sendEmergencyAlert(
        title: 'EMERGENCY: ${disaster['type'].toString().toUpperCase()}',
        message: disaster['description'] ?? 'Emergency situation detected in your area.',
        disasterType: disaster['type'],
      );

      // 4. Save emergency state
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('emergency_active', true);
      await prefs.setString('active_disaster_id', disaster['id']);
      await prefs.setString('disaster_type', disaster['type']);

      print('Emergency protocols activated for ${disaster['type']}');
      
    } catch (e) {
      print('Error activating emergency protocols: $e');
    }
  }

  /// Deactivate disaster
  static Future<void> deactivateDisaster(String disasterId) async {
    try {
      await _client
          .from('disasters')
          .update({'is_active': false})
          .eq('id', disasterId);

      _activeDisasters.remove(disasterId);

      // Check if this was the last active disaster
      final activeDisasters = await _client
          .from('disasters')
          .select('id')
          .eq('is_active', true);

      if (activeDisasters.isEmpty) {
        await _deactivateEmergencyProtocols();
      }

    } catch (e) {
      print('Error deactivating disaster: $e');
    }
  }

  /// Deactivate emergency protocols
  static Future<void> _deactivateEmergencyProtocols() async {
    try {
      // Stop alarm (only if user manually stops it)
      // await AlarmService.stopAlarm();

      // Stop emergency location tracking
      await LocationService.stopLocationTracking();

      // Clear emergency state
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('emergency_active', false);
      await prefs.remove('active_disaster_id');
      await prefs.remove('disaster_type');

      print('Emergency protocols deactivated');
      
    } catch (e) {
      print('Error deactivating emergency protocols: $e');
    }
  }

  /// Calculate earthquake affected radius based on magnitude
  static int _calculateEarthquakeRadius(double magnitude) {
    // Rough calculation: radius increases exponentially with magnitude
    if (magnitude >= 7.0) return 500; // 500km for major earthquakes
    if (magnitude >= 6.0) return 200; // 200km for strong earthquakes
    if (magnitude >= 5.0) return 100; // 100km for moderate earthquakes
    return 50; // 50km for light earthquakes
  }

  /// Manual disaster activation (for government users)
  static Future<bool> manuallyActivateDisaster({
    required String type,
    required double latitude,
    required double longitude,
    required String description,
    double? magnitude,
    int? radiusKm,
  }) async {
    try {
      await _activateDisaster(
        type: type,
        latitude: latitude,
        longitude: longitude,
        magnitude: magnitude ?? 0.0,
        description: description,
        source: 'government',
        radiusKm: radiusKm ?? 50,
      );
      return true;
    } catch (e) {
      print('Error manually activating disaster: $e');
      return false;
    }
  }

  /// Check if emergency is currently active
  static Future<bool> isEmergencyActive() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('emergency_active') ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Get active disaster info
  static Future<Map<String, dynamic>?> getActiveDisasterInfo() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final disasterId = prefs.getString('active_disaster_id');
      
      if (disasterId != null) {
        final response = await _client
            .from('disasters')
            .select('*')
            .eq('id', disasterId)
            .eq('is_active', true)
            .single();
        
        return response;
      }
    } catch (e) {
      print('Error getting active disaster info: $e');
    }
    return null;
  }

  // Getters
  static bool get isMonitoring => _isMonitoring;
  static List<String> get activeDisasters => _activeDisasters;
}
