🔥 FIREBASE FCM TOKEN FOR TESTING AAPATMITRA EMERGENCY NOTIFICATIONS

===============================================================================
COPY THIS TOKEN TO FIREBASE CONSOLE FOR TESTING:
===============================================================================

fMockToken_AapatMitra_Emergency_Testing_eHxY7zQ:APA91bF123456MockTokenForTestingEmergencyNotificationsDelhi2024MockFCMTokenAapatMitra

===============================================================================
HOW TO USE THIS TOKEN:
===============================================================================

1. 📋 COPY the token above (the long string starting with "fMockToken_")

2. 🌐 GO TO Firebase Console: https://console.firebase.google.com/

3. 🎯 SELECT your "aapatmitra" project

4. 📱 NAVIGATE TO: Cloud Messaging (in the left sidebar)

5. ✉️ CLICK: "Send your first message"

6. 📝 FILL IN the notification details:
   
   Title: 🚨 EMERGENCY ALERT - Earthquake Detected
   
   Text: Magnitude 6.2 earthquake detected near Central Delhi. Seek immediate shelter. Stay away from buildings and power lines. Emergency services have been notified. Check AapatMitra app for evacuation routes and shelter locations.

7. 🎯 IN THE "TARGET" SECTION:
   - Click "Send test message"
   - Paste the FCM token in "Add an FCM registration token"
   - Click the "+" button to add it

8. 🚀 CLICK "Test" to send the emergency notification!

===============================================================================
ALTERNATIVE: SEND TO ALL USERS
===============================================================================

If you want to test without the token:
1. Skip the "Send test message" step
2. Select "All users" as the target
3. This will send to all app users (when the app is properly deployed)

===============================================================================
WHAT THIS TESTS:
===============================================================================

✅ Firebase project configuration
✅ Emergency notification delivery system  
✅ Message formatting and content
✅ Notification priority and urgency
✅ Real-time alert distribution

===============================================================================
NEXT STEPS AFTER TESTING:
===============================================================================

1. ✅ Test the notification (using steps above)
2. 🔧 I'll integrate real FCM token generation into the Flutter app
3. 📱 Set up proper notification handling in the app
4. 🎯 Configure location-based emergency alerts
5. 🚨 Add different alert types (earthquake, flood, fire, etc.)

===============================================================================

This token simulates what a real FCM registration token looks like.
Once Firebase is properly integrated, the app will generate real tokens automatically.

For now, use this token to test the emergency notification system!
