import 'dart:convert';
import 'package:http/http.dart' as http;

class WeatherService {
  static const String _baseUrl = 'https://api.weatherapi.com/v1';
  final String _apiKey = 'a4618e4f5c424aa4a73134941253005';

  // Get current weather for a location
  Future<Map<String, dynamic>> getCurrentWeather({
    required double latitude,
    required double longitude,
  }) async {
    try {
      final queryParams = {
        'key': _apiKey,
        'q': '$latitude,$longitude',
        'aqi': 'yes', // Include air quality data
      };

      final uri = Uri.parse('$_baseUrl/current.json').replace(queryParameters: queryParams);
      final response = await http.get(uri);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to load weather data: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching weather data: $e');
    }
  }

  // Get weather alerts for a location
  Future<Map<String, dynamic>> getWeatherAlerts({
    required double latitude,
    required double longitude,
  }) async {
    try {
      final queryParams = {
        'key': _apiKey,
        'q': '$latitude,$longitude',
        'alerts': 'yes',
      };

      final uri = Uri.parse('$_baseUrl/forecast.json').replace(queryParameters: queryParams);
      final response = await http.get(uri);

      if (response.statusCode == 200) {
        return json.decode(response.body);
      } else {
        throw Exception('Failed to load weather alerts: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Error fetching weather alerts: $e');
    }
  }

  // Get severe weather warnings
  Future<List<Map<String, dynamic>>> getSevereWeatherWarnings({
    required double latitude,
    required double longitude,
  }) async {
    try {
      final weatherData = await getWeatherAlerts(
        latitude: latitude,
        longitude: longitude,
      );

      final alerts = weatherData['alerts']?['alert'] as List? ?? [];
      return alerts
          .where((alert) => 
              alert['severity'] == 'Extreme' || 
              alert['severity'] == 'Severe')
          .map((alert) => Map<String, dynamic>.from(alert))
          .toList();
    } catch (e) {
      throw Exception('Error fetching severe weather warnings: $e');
    }
  }
} 