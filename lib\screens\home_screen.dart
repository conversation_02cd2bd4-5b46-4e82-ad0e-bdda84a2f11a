import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/disaster_provider.dart';
import '../providers/user_provider.dart';
import '../services/government_service.dart';
import 'government/government_dashboard.dart';

final _safetyStatusProvider = StateProvider<String>((ref) => 'safe');

class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final disaster = ref.watch(disasterProvider);
    // final user = ref.watch(userProvider); // User from provider if needed for other parts
    final safetyStatus = ref.watch(_safetyStatusProvider);
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFFF5ECE7), Color(0xFFE2D6CE)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: <PERSON><PERSON><PERSON>(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 16.0),
                        child: Text('Welcome, [${ref.read(userProvider).name.split(" ").first}]!',
                            style: textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)),
                      ),
                    ),
                    IconButton(
                      onPressed: () async {
                        final govUser = await GovernmentService.getCurrentGovernmentUser();
                        if (govUser != null) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(builder: (_) => const GovernmentDashboard()),
                          );
                        } else {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Government access required')),
                          );
                        }
                      },
                      icon: const Icon(Icons.admin_panel_settings),
                      tooltip: 'Government Dashboard',
                    ),
                  ],
                ),
                // Search bar
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.brown.withOpacity(0.08),
                        spreadRadius: 1,
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: TextField(
                    decoration: InputDecoration(
                      hintText: 'Search for a location...',
                      prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                    ),
                    style: textTheme.bodyLarge,
                  ),
                ),
                const SizedBox(height: 16),
                // India map image (replace with your asset if available)
                Container(
                  height: 160,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardTheme.color ?? Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.brown.withOpacity(0.08),
                        spreadRadius: 1,
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.map,
                          size: 64,
                          color: Colors.brown.shade300,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Emergency Map View',
                          style: TextStyle(
                            color: Colors.brown.shade600,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Real-time disaster tracking',
                          style: TextStyle(
                            color: Colors.brown.shade500,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Disaster alert carousel
                SizedBox(
                  height: 110,
                  child: PageView(
                    children: [
                      Card(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.8),
                        elevation: 2,
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    disaster.type == 'flood' ? Icons.water_drop : Icons.warning_amber_rounded,
                                    color: Theme.of(context).colorScheme.onPrimary,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    disaster.type == 'flood' ? 'Flood Alert' : 'Earthquake Alert',
                                    style: textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.bold, color: Theme.of(context).colorScheme.onPrimary),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                disaster.type == 'flood'
                                    ? 'Heavy rainfall expected. Avoid low-lying areas.'
                                    : 'Earthquake in [${disaster.location}] (Mag [${disaster.magnitude}]). Stay safe!',
                                style: textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.onPrimary),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12.0),
                    child: Chip(
                      avatar: Icon(
                        safetyStatus == 'safe'
                            ? Icons.check_circle
                            : (safetyStatus == 'needs_help' ? Icons.error : Icons.warning),
                        color: Colors.white,
                        size: 18,
                      ),
                      label: Text(
                        'Status: [${safetyStatus == 'needs_help' ? 'Needs Help' : safetyStatus[0].toUpperCase() + safetyStatus.substring(1)}]',
                        style: textTheme.labelLarge?.copyWith(color: Colors.white, fontWeight: FontWeight.bold),
                      ),
                      backgroundColor: safetyStatus == 'safe'
                          ? Colors.green.shade600
                          : (safetyStatus == 'needs_help' ? Colors.red.shade600 : Colors.orange.shade600),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                // Update status button
                Align(
                  alignment: Alignment.center,
                  child: SizedBox(
                    width: double.infinity,
                    child: TweenAnimationBuilder<double>(
                      tween: Tween(begin: 1.0, end: 1.0), // No actual animation, can be simplified or removed if not used
                      duration: const Duration(milliseconds: 500),
                      builder: (context, scale, child) {
                        return Transform.scale(
                          scale: scale,
                          child: ElevatedButton(
                            onPressed: () async {
                              final newStatus = await showModalBottomSheet<String>(
                                context: context,
                                builder: (context) => _StatusModal(current: safetyStatus),
                              );
                              if (newStatus != null && newStatus != safetyStatus) {
                                ref.read(_safetyStatusProvider.notifier).state = newStatus;
                              }
                            },
                            child: const Text('Update Safety Status'),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Quick Actions
                Text(
                  'Quick Actions',
                  style: textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _QuickActionCard(
                        icon: Icons.location_on,
                        title: 'Share Location',
                        subtitle: 'Send current location',
                        color: Colors.blue,
                        onTap: () => _shareLocation(context),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _QuickActionCard(
                        icon: Icons.warning,
                        title: 'Report Emergency',
                        subtitle: 'Report incident',
                        color: Colors.orange,
                        onTap: () => _reportEmergency(context),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _QuickActionCard(
                        icon: Icons.phone,
                        title: 'Emergency Call',
                        subtitle: 'Call 112',
                        color: Colors.green,
                        onTap: () => _makeEmergencyCall(context),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _QuickActionCard(
                        icon: Icons.sos,
                        title: 'SOS Alert',
                        subtitle: 'Send SOS signal',
                        color: Colors.red,
                        onTap: () => _sendSOS(context),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _StatusModal extends StatelessWidget {
  final String current;
  const _StatusModal({required this.current});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final statuses = [
      {'label': 'Safe', 'value': 'safe', 'color': Colors.green},
      {'label': 'Unsafe', 'value': 'unsafe', 'color': Colors.orange},
      {'label': 'Needs Help', 'value': 'needs_help', 'color': Colors.red},
    ];
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text('Update Your Safety Status', style: textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
          ),
          const SizedBox(height: 16),
          ...statuses.map((status) => ListTile(
                leading: Icon(Icons.circle, color: status['color'] as Color, size: 20),
                title: Text(status['label'] as String, style: textTheme.titleMedium),
                trailing: current == status['value']
                    ? Icon(Icons.check_circle, color: Theme.of(context).colorScheme.secondary)
                    : null,
                onTap: () => Navigator.pop(context, status['value'] as String),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                contentPadding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
              )),
        ],
      ),
    );
  }

  // Quick action methods
  static void _shareLocation(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Location shared with emergency contacts'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  static void _reportEmergency(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report Emergency'),
        content: const Text('What type of emergency would you like to report?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Emergency report submitted'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            child: const Text('Submit'),
          ),
        ],
      ),
    );
  }

  static void _makeEmergencyCall(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Emergency Call'),
        content: const Text('Call emergency services (112)?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Calling emergency services...'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Call'),
          ),
        ],
      ),
    );
  }

  static void _sendSOS(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('SOS Alert'),
        content: const Text('Send SOS signal to all emergency contacts and authorities?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('SOS signal sent!'),
                  backgroundColor: Colors.red,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Send SOS'),
          ),
        ],
      ),
    );
  }
}

class _QuickActionCard extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final VoidCallback onTap;

  const _QuickActionCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              CircleAvatar(
                backgroundColor: color.withOpacity(0.1),
                child: Icon(icon, color: color),
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
                textAlign: TextAlign.center,
              ),
              Text(
                subtitle,
                style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}