import 'package:supabase_flutter/supabase_flutter.dart';

class AuthService {
  static SupabaseClient get client => Supabase.instance.client;

  // --- STATIC WRAPPERS for UI compatibility ---
  static Future<AuthResponse> login({required String email, required String password}) {
    return AuthService().signIn(email: email, password: password);
  }

  static Future<AuthResponse> signup({required String name, required String email, required String phone, required String password}) {
    return AuthService().signUp(email: email, password: password, phone: phone, name: name);
  }

  static Future<void> sendOtp(String phone) {
    return AuthService().signInWithPhone(phone: phone);
  }

  static Future<AuthResponse> verifyOtp(String phone, String otp) {
    return AuthService().verifyOTP(phone: phone, otp: otp);
  }

  // Sign up with email and password
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    required String phone,
    required String name,
  }) async {
    try {
      final response = await client.auth.signUp(
        email: email,
        password: password,
        data: {
          'phone': phone,
          'name': name,
        },
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Sign in with email and password
  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    try {
      final response = await client.auth.signInWithPassword(
        email: email,
        password: password,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Sign in with phone number
  Future<void> signInWithPhone({
    required String phone,
  }) async {
    try {
      await client.auth.signInWithOtp(
        phone: phone,
      );
    } catch (e) {
      rethrow;
    }
  }

  // Verify OTP
  Future<AuthResponse> verifyOTP({
    required String phone,
    required String otp,
  }) async {
    try {
      final response = await client.auth.verifyOTP(
        phone: phone,
        token: otp,
        type: OtpType.sms,
      );
      return response;
    } catch (e) {
      rethrow;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await client.auth.signOut();
    } catch (e) {
      rethrow;
    }
  }

  // Get current user
  User? get currentUser => client.auth.currentUser;

  // Check if user is authenticated
  bool get isAuthenticated => currentUser != null;

  // Stream of auth state changes
  Stream<AuthState> get authStateChanges => client.auth.onAuthStateChange;
}