import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/firebase_config.dart';
import '../utils/fcm_token_generator.dart';

class FirebaseService {
  static String? _fcmToken;
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;

  /// Initialize Firebase
  static Future<void> initialize() async {
    try {
      // Initialize Firebase Core
      await Firebase.initializeApp(
        options: FirebaseConfig.currentPlatform,
      );

      // Initialize Firebase Messaging if not web
      if (!kIsWeb) {
        // Request permission
        NotificationSettings settings = await _messaging.requestPermission(
          alert: true,
          badge: true,
          sound: true,
          provisional: false,
        );

        print('User granted permission: ${settings.authorizationStatus}');

        // Get FCM token
        _fcmToken = await _messaging.getToken();
        if (_fcmToken != null) {
          print('FCM Token: $_fcmToken');
          // Save token to local storage
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('fcm_token', _fcmToken!);
        }

        // Listen for token refresh
        _messaging.onTokenRefresh.listen((token) async {
          _fcmToken = token;
          final prefs = await SharedPreferences.getInstance();
          await prefs.setString('fcm_token', token);
          print('FCM Token refreshed: $token');
        });
      }

      print('Firebase initialized successfully');
    } catch (e) {
      print('Error initializing Firebase: $e');
      // Continue without Firebase for now
    }
  }

  /// Get FCM registration token
  static Future<String?> getFCMToken() async {
    try {
      if (kIsWeb) {
        // For web, return mock token
        _fcmToken = FCMTokenGenerator.generateMockToken();
      } else {
        // For mobile, get real token
        _fcmToken = await _messaging.getToken();
      }

      if (_fcmToken != null) {
        // Save token to local storage
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fcm_token', _fcmToken!);
      }

      print('FCM Token: $_fcmToken');
      return _fcmToken;
    } catch (e) {
      print('Error getting FCM token: $e');
      return null;
    }
  }

  /// Get cached FCM token
  static Future<String?> getCachedFCMToken() async {
    try {
      if (_fcmToken != null) return _fcmToken;

      final prefs = await SharedPreferences.getInstance();
      _fcmToken = prefs.getString('fcm_token');
      return _fcmToken;
    } catch (e) {
      print('Error getting cached FCM token: $e');
      return null;
    }
  }

  /// Display FCM token for testing
  static Future<void> displayTokenForTesting() async {
    try {
      String? token = await getFCMToken();
      if (token != null) {
        print('=== FCM TOKEN FOR TESTING ===');
        print(token);
        print('=============================');
        print('Copy this token to Firebase Console for testing notifications');
      } else {
        // Fallback to mock token generator
        FCMTokenGenerator.displayTokenForTesting();
      }
    } catch (e) {
      print('Error displaying FCM token: $e');
      // Fallback to mock token
      FCMTokenGenerator.displayTokenForTesting();
    }
  }

  /// Check if Firebase is available
  static bool get isAvailable => !kIsWeb;

  /// Get current token (cached)
  static String? get currentToken => _fcmToken;

  /// Get Firebase Messaging instance
  static FirebaseMessaging get messaging => _messaging;
}
