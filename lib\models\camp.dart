class Camp {
  final String id;
  final String name;
  final String address;
  final String type; // 'evacuation_center', 'medical_camp', 'food_distribution', 'shelter'
  final int capacity;
  final int currentOccupancy;
  final List<String> facilities; // ['food', 'water', 'medical', 'shelter', 'power', 'wifi']
  final Map<String, dynamic> contactInfo;
  final bool isActive;
  final List<String> accessibilityFeatures;
  final Map<String, dynamic> operatingHours;
  final Map<String, int> resourceLevels; // {'food': 80, 'water': 60, 'medical': 90}
  final DateTime lastUpdated;
  final double latitude;
  final double longitude;

  Camp({
    required this.id,
    required this.name,
    required this.address,
    required this.type,
    required this.capacity,
    required this.currentOccupancy,
    required this.facilities,
    required this.contactInfo,
    required this.isActive,
    required this.accessibilityFeatures,
    required this.operatingHours,
    required this.resourceLevels,
    required this.lastUpdated,
    required this.latitude,
    required this.longitude,
  });

  factory Camp.fromJson(Map<String, dynamic> json) {
    // Parse location from PostGIS POINT format
    final locationStr = json['location'] as String?;
    double lat = 0.0, lng = 0.0;
    if (locationStr != null) {
      final coords = locationStr.replaceAll(RegExp(r'[POINT()]'), '').split(' ');
      if (coords.length >= 2) {
        lng = double.tryParse(coords[0]) ?? 0.0;
        lat = double.tryParse(coords[1]) ?? 0.0;
      }
    }

    return Camp(
      id: json['id'] as String,
      name: json['name'] as String,
      address: json['address'] as String,
      type: json['type'] as String,
      capacity: json['capacity'] as int,
      currentOccupancy: json['current_occupancy'] as int? ?? 0,
      facilities: List<String>.from(json['facilities'] as List? ?? []),
      contactInfo: Map<String, dynamic>.from(json['contact_info'] as Map? ?? {}),
      isActive: json['is_active'] as bool? ?? true,
      accessibilityFeatures: List<String>.from(json['accessibility_features'] as List? ?? []),
      operatingHours: Map<String, dynamic>.from(json['operating_hours'] as Map? ?? {}),
      resourceLevels: Map<String, int>.from(
        (json['resource_levels'] as Map? ?? {}).map((k, v) => MapEntry(k.toString(), v as int? ?? 0))
      ),
      lastUpdated: DateTime.parse(json['last_updated'] as String),
      latitude: lat,
      longitude: lng,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'type': type,
      'capacity': capacity,
      'current_occupancy': currentOccupancy,
      'facilities': facilities,
      'contact_info': contactInfo,
      'is_active': isActive,
      'accessibility_features': accessibilityFeatures,
      'operating_hours': operatingHours,
      'resource_levels': resourceLevels,
      'last_updated': lastUpdated.toIso8601String(),
      'location': 'POINT($longitude $latitude)',
    };
  }

  // Helper methods
  String get occupancyStatus {
    final percentage = (currentOccupancy / capacity * 100).round();
    if (percentage >= 90) return 'Full';
    if (percentage >= 70) return 'High';
    if (percentage >= 40) return 'Medium';
    return 'Available';
  }

  String getResourceStatus(String resource) {
    final level = resourceLevels[resource] ?? 0;
    if (level >= 80) return 'Good';
    if (level >= 50) return 'Medium';
    if (level >= 20) return 'Low';
    return 'Critical';
  }

  bool get hasFood => facilities.contains('food');
  bool get hasWater => facilities.contains('water');
  bool get hasMedical => facilities.contains('medical');
  bool get hasShelter => facilities.contains('shelter');
  bool get hasPower => facilities.contains('power');
  bool get hasWifi => facilities.contains('wifi');

  // Mock camps for testing
  static List<Camp> mockList() => [
    Camp(
      id: '1',
      name: 'Central Evacuation Center',
      address: 'Connaught Place, New Delhi',
      type: 'evacuation_center',
      capacity: 500,
      currentOccupancy: 120,
      facilities: ['food', 'water', 'medical', 'shelter', 'power', 'wifi'],
      contactInfo: {'phone': '+91-11-23456789', 'email': '<EMAIL>'},
      isActive: true,
      accessibilityFeatures: ['wheelchair', 'elderly_care'],
      operatingHours: {'open': '24/7'},
      resourceLevels: {'food': 85, 'water': 70, 'medical': 90, 'shelter': 76},
      lastUpdated: DateTime.now(),
      latitude: 28.6139,
      longitude: 77.2090,
    ),
    Camp(
      id: '2',
      name: 'Medical Relief Camp Alpha',
      address: 'India Gate, New Delhi',
      type: 'medical_camp',
      capacity: 200,
      currentOccupancy: 45,
      facilities: ['medical', 'water', 'power'],
      contactInfo: {'phone': '+91-11-23456790', 'email': '<EMAIL>'},
      isActive: true,
      accessibilityFeatures: ['wheelchair'],
      operatingHours: {'open': '24/7'},
      resourceLevels: {'medical': 95, 'water': 80},
      lastUpdated: DateTime.now(),
      latitude: 28.6333,
      longitude: 77.2167,
    ),
    Camp(
      id: '3',
      name: 'Emergency Shelter Beta',
      address: 'Red Fort, New Delhi',
      type: 'shelter',
      capacity: 400,
      currentOccupancy: 320,
      facilities: ['shelter', 'food', 'water', 'power'],
      contactInfo: {'phone': '+91-11-23456792'},
      isActive: true,
      accessibilityFeatures: ['wheelchair', 'elderly_care', 'child_care'],
      operatingHours: {'open': '24/7'},
      resourceLevels: {'food': 40, 'water': 55, 'shelter': 80},
      lastUpdated: DateTime.now(),
      latitude: 28.6129,
      longitude: 77.2411,
    ),
  ];
}