class Camp {
  final String id;
  final String name;
  final int capacity;
  final int occupancy;
  final String foodStatus;
  final String waterStatus;

  Camp({
    required this.id,
    required this.name,
    required this.capacity,
    required this.occupancy,
    required this.foodStatus,
    required this.waterStatus,
  });

  // Mock camps
  static List<Camp> mockList() => [
    Camp(id: '1', name: 'Camp Alpha', capacity: 200, occupancy: 120, foodStatus: 'Good', waterStatus: 'Low'),
    Camp(id: '2', name: 'Camp Bravo', capacity: 150, occupancy: 80, foodStatus: 'Low', waterStatus: 'Good'),
    Camp(id: '3', name: 'Camp Charlie', capacity: 250, occupancy: 200, foodStatus: 'Good', waterStatus: 'Good'),
  ];
} 