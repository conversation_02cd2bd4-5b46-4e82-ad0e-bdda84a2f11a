class Camp {
  final String id;
  final String name;
  final String address;
  final double latitude;
  final double longitude;
  final int capacity;
  final int currentOccupancy;
  final Map<String, dynamic> facilities;
  final bool isActive;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Camp({
    required this.id,
    required this.name,
    required this.address,
    required this.latitude,
    required this.longitude,
    required this.capacity,
    required this.currentOccupancy,
    required this.facilities,
    required this.isActive,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
  });

  factory Camp.fromJson(Map<String, dynamic> json) {
    return Camp(
      id: json['id'],
      name: json['name'],
      address: json['address'],
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      capacity: json['capacity'],
      currentOccupancy: json['current_occupancy'],
      facilities: json['facilities'],
      isActive: json['is_active'],
      createdBy: json['created_by'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'capacity': capacity,
      'current_occupancy': currentOccupancy,
      'facilities': facilities,
      'is_active': isActive,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  String getResourceStatus(String resource) {
    if (!facilities.containsKey(resource)) return 'N/A';
    final status = facilities[resource]['status'];
    return status.toString().toUpperCase();
  }

  int getResourceQuantity(String resource) {
    if (!facilities.containsKey(resource)) return 0;
    return facilities[resource]['quantity'] ?? 0;
  }

  bool hasResource(String resource) {
    return facilities.containsKey(resource);
  }

  // Helper methods
  String get occupancyStatus {
    final percentage = (currentOccupancy / capacity * 100).round();
    if (percentage >= 90) return 'Full';
    if (percentage >= 70) return 'High';
    if (percentage >= 40) return 'Medium';
    return 'Available';
  }

  // Mock camps for testing
  static List<Camp> mockList() => [
    Camp(
      id: '1',
      name: 'Central Evacuation Center',
      address: 'Connaught Place, New Delhi',
      latitude: 28.6139,
      longitude: 77.2090,
      capacity: 500,
      currentOccupancy: 120,
      facilities: {'food': {'status': 'Good', 'quantity': 85}, 'water': {'status': 'Good', 'quantity': 70}, 'medical': {'status': 'Good', 'quantity': 90}, 'shelter': {'status': 'Good', 'quantity': 76}, 'power': {'status': 'Good', 'quantity': 80}, 'wifi': {'status': 'Good', 'quantity': 70}},
      isActive: true,
      createdBy: '<EMAIL>',
      createdAt: DateTime.now(),
    ),
    Camp(
      id: '2',
      name: 'Medical Relief Camp Alpha',
      address: 'India Gate, New Delhi',
      latitude: 28.6333,
      longitude: 77.2167,
      capacity: 200,
      currentOccupancy: 45,
      facilities: {'medical': {'status': 'Good', 'quantity': 95}, 'water': {'status': 'Good', 'quantity': 80}, 'power': {'status': 'Good', 'quantity': 80}},
      isActive: true,
      createdBy: '<EMAIL>',
      createdAt: DateTime.now(),
    ),
    Camp(
      id: '3',
      name: 'Emergency Shelter Beta',
      address: 'Red Fort, New Delhi',
      latitude: 28.6129,
      longitude: 77.2411,
      capacity: 400,
      currentOccupancy: 320,
      facilities: {'shelter': {'status': 'Good', 'quantity': 80}, 'food': {'status': 'Low', 'quantity': 40}, 'water': {'status': 'Medium', 'quantity': 55}, 'power': {'status': 'Good', 'quantity': 80}},
      isActive: true,
      createdBy: '+91-11-23456792',
      createdAt: DateTime.now(),
    ),
  ];
}