import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/firebase_config.dart';
import '../utils/fcm_token_generator.dart';

class FirebaseService {
  static FirebaseMessaging? _messaging;
  static String? _fcmToken;

  /// Initialize Firebase
  static Future<void> initialize() async {
    try {
      // Initialize Firebase Core
      await Firebase.initializeApp(
        options: FirebaseConfig.currentPlatform,
      );

      // Initialize Firebase Messaging
      _messaging = FirebaseMessaging.instance;

      print('Firebase initialized successfully');
    } catch (e) {
      print('Error initializing Firebase: $e');
      // Continue without Firebase for now
    }
  }

  /// Get FCM registration token
  static Future<String?> getFCMToken() async {
    try {
      if (_messaging == null) {
        print('Firebase Messaging not initialized');
        return null;
      }

      // Skip on web for now
      if (kIsWeb) {
        print('FCM token generation skipped on web');
        return null;
      }

      // Request permission first
      NotificationSettings settings = await _messaging!.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: false,
      );

      if (settings.authorizationStatus != AuthorizationStatus.authorized) {
        print('User denied notification permissions');
        return null;
      }

      // Get the token
      _fcmToken = await _messaging!.getToken();

      if (_fcmToken != null) {
        print('FCM Token: $_fcmToken');

        // Save token to local storage
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString('fcm_token', _fcmToken!);

        return _fcmToken;
      }

      return null;
    } catch (e) {
      print('Error getting FCM token: $e');
      return null;
    }
  }

  /// Get cached FCM token
  static Future<String?> getCachedFCMToken() async {
    try {
      if (_fcmToken != null) return _fcmToken;

      final prefs = await SharedPreferences.getInstance();
      _fcmToken = prefs.getString('fcm_token');
      return _fcmToken;
    } catch (e) {
      print('Error getting cached FCM token: $e');
      return null;
    }
  }

  /// Listen for token refresh
  static void listenForTokenRefresh() {
    try {
      if (_messaging == null) return;

      _messaging!.onTokenRefresh.listen((newToken) {
        _fcmToken = newToken;
        print('FCM Token refreshed: $newToken');

        // Save new token
        SharedPreferences.getInstance().then((prefs) {
          prefs.setString('fcm_token', newToken);
        });
      });
    } catch (e) {
      print('Error setting up token refresh listener: $e');
    }
  }

  /// Display FCM token for testing
  static Future<void> displayTokenForTesting() async {
    try {
      String? token = await getFCMToken();
      if (token != null) {
        print('=== FCM TOKEN FOR TESTING ===');
        print(token);
        print('=============================');
        print('Copy this token to Firebase Console for testing notifications');
      } else {
        // If Firebase is not working, use mock token for testing
        print('Firebase not available, generating mock token for testing...');
        FCMTokenGenerator.displayTokenForTesting();
      }
    } catch (e) {
      print('Error displaying FCM token: $e');
      // Fallback to mock token
      print('Using mock token for testing...');
      FCMTokenGenerator.displayTokenForTesting();
    }
  }

  /// Check if Firebase is available
  static bool get isAvailable => _messaging != null;

  /// Get current token (cached)
  static String? get currentToken => _fcmToken;
}
