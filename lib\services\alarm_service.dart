import 'dart:async';
import 'package:audioplayers/audioplayers.dart';
import 'package:vibration/vibration.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';

class AlarmService {
  static AudioPlayer? _audioPlayer;
  static Timer? _alarmTimer;
  static Timer? _vibrationTimer;
  static bool _isEmergencyAlarmActive = false;
  static bool _userStoppedAlarm = false;
  static int _alarmVolume = 100;

  // Alarm patterns
  static const List<int> _emergencyVibrationPattern = [500, 1000, 500, 1000];
  static const Duration _alarmInterval = Duration(seconds: 2);

  /// Initialize alarm service
  static Future<void> initialize() async {
    _audioPlayer = AudioPlayer();
    await _loadAlarmSettings();
  }

  /// Start emergency alarm (cannot be stopped programmatically)
  static Future<void> startEmergencyAlarm() async {
    if (_isEmergencyAlarmActive) return;
    _isEmergencyAlarmActive = true;

    try {
      await WakelockPlus.enable();
      await _playEmergencyAlarm();
      await _startEmergencyVibration();
      await _saveAlarmState(true);
    } catch (e) {
      print('Error starting emergency alarm: $e');
    }
  }

  /// Stop alarm (only when user manually stops it)
  static Future<void> stopEmergencyAlarm() async {
    if (!_isEmergencyAlarmActive) return;

    try {
      await _audioPlayer?.stop();
      await Vibration.cancel();
      _isEmergencyAlarmActive = false;
      if (_alarmTimer != null) {
        _alarmTimer!.cancel();
        _alarmTimer = null;
      }
    } catch (e) {
      print('Error stopping emergency alarm: $e');
    }

    await WakelockPlus.disable();
    await _saveAlarmState(false);
  }

  /// Internal method to stop all alarm components
  static Future<void> _stopAllAlarms() async {
    _isEmergencyAlarmActive = false;

    // Stop audio
    await _audioPlayer?.stop();
    _alarmTimer?.cancel();
    _alarmTimer = null;

    // Stop vibration
    _vibrationTimer?.cancel();
    _vibrationTimer = null;
    if (!kIsWeb) {
      try {
        await Vibration.cancel();
      } catch (e) {
        // Ignore vibration errors on web
      }
    }

    // Release wakelock
    await WakelockPlus.disable();

    // Save state
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('emergency_alarm_active', false);
  }

  /// Start audio alarm with continuous loop
  static Future<void> _playEmergencyAlarm() async {
    try {
      if (_audioPlayer == null) {
        await _initializeAudioPlayer();
      }

      await _audioPlayer?.play(AssetSource('sounds/emergency_alarm.mp3'));

      _alarmTimer = Timer.periodic(const Duration(seconds: 10), (timer) async {
        if (_isEmergencyAlarmActive) {
          await _audioPlayer?.play(AssetSource('sounds/emergency_alarm.mp3'));
        } else {
          timer.cancel();
        }
      });
    } catch (e) {
      print('Error playing emergency alarm: $e');
    }
  }

  /// Start vibration alarm
  static Future<void> _startEmergencyVibration() async {
    try {
      bool? hasVibrator = await Vibration.hasVibrator();
      if (hasVibrator != null && hasVibrator) {
        _alarmTimer = Timer.periodic(const Duration(seconds: 2), (timer) async {
          if (_isEmergencyAlarmActive) {
            await Vibration.vibrate(pattern: _emergencyVibrationPattern);
          } else {
            timer.cancel();
          }
        });
      }
    } catch (e) {
      print('Error starting vibration: $e');
    }
  }

  /// Load alarm settings from preferences
  static Future<void> _loadAlarmSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _alarmVolume = prefs.getInt('alarm_volume') ?? 100;

      // Check if alarm was active before app restart
      bool wasAlarmActive = prefs.getBool('emergency_alarm_active') ?? false;
      if (wasAlarmActive && !_userStoppedAlarm) {
        // Restart alarm if it was active
        await startEmergencyAlarm();
      }

    } catch (e) {
      print('Error loading alarm settings: $e');
    }
  }

  /// Set alarm volume (0-100)
  static Future<void> setAlarmVolume(int volume) async {
    try {
      _alarmVolume = volume.clamp(0, 100);
      await _audioPlayer?.setVolume(_alarmVolume / 100.0);

      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt('alarm_volume', _alarmVolume);

    } catch (e) {
      print('Error setting alarm volume: $e');
    }
  }

  /// Test alarm (for settings)
  static Future<void> testAlarm() async {
    try {
      await _audioPlayer?.play(AssetSource('sounds/emergency_alarm.mp3'));
      if (!kIsWeb) {
        await Vibration.vibrate(pattern: _emergencyVibrationPattern);
      }
    } catch (e) {
      await SystemSound.play(SystemSoundType.alert);
    }
  }

  /// Check if alarm should be restarted (for app lifecycle management)
  static Future<void> checkAndRestoreAlarmState() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final wasActive = prefs.getBool('emergency_alarm_active') ?? false;
      if (wasActive) {
        await startEmergencyAlarm();
      }
    } catch (e) {
      print('Error restoring alarm state: $e');
    }
  }

  /// Get alarm statistics
  static Future<Map<String, dynamic>> getAlarmStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      return {
        'is_active': _isEmergencyAlarmActive,
        'user_stopped': _userStoppedAlarm,
        'volume': _alarmVolume,
        'start_time': prefs.getString('alarm_timestamp'),
      };

    } catch (e) {
      return {
        'is_active': _isEmergencyAlarmActive,
        'user_stopped': _userStoppedAlarm,
        'volume': _alarmVolume,
      };
    }
  }

  /// Dispose resources
  static Future<void> dispose() async {
    await _stopAllAlarms();
    await _audioPlayer?.dispose();
    _audioPlayer = null;
  }

  // Getters
  static bool get isEmergencyAlarmActive => _isEmergencyAlarmActive;
  static bool get userStoppedAlarm => _userStoppedAlarm;
  static int get alarmVolume => _alarmVolume;

  static Future<void> _initializeAudioPlayer() async {
    try {
      _audioPlayer = AudioPlayer();
    } catch (e) {
      print('Error initializing audio player: $e');
    }
  }

  static Future<void> _saveAlarmState(bool isActive) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('emergency_alarm_active', isActive);
      await prefs.setString('alarm_timestamp', DateTime.now().toIso8601String());
    } catch (e) {
      print('Error saving alarm state: $e');
    }
  }
}
