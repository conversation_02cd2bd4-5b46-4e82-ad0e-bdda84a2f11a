import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/government_user.dart';
import '../models/camp.dart';
import '../models/government_alert.dart' as alerts;
import 'disaster_detection_service.dart';
import 'alert_service.dart';

class GovernmentService {
  static final SupabaseClient _client = Supabase.instance.client;

  /// Check if current user is a government user
  static Future<GovernmentUser?> getCurrentGovernmentUser() async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) return null;

      final response = await _client
          .from('government_users')
          .select('*')
          .eq('user_id', user.id)
          .single();

      return GovernmentUser.fromJson(response);
    } catch (e) {
      print('Error getting government user: $e');
      return null;
    }
  }

  /// Get all government users (admin only)
  static Future<List<GovernmentUser>> getAllGovernmentUsers() async {
    try {
      final response = await _client
          .from('government_users')
          .select('*')
          .order('created_at', ascending: false);

      return response.map<GovernmentUser>((json) => GovernmentUser.fromJson(json)).toList();
    } catch (e) {
      print('Error getting government users: $e');
      return [];
    }
  }

  /// Create new government user (admin only)
  static Future<bool> createGovernmentUser({
    required String userId,
    required String department,
    required int clearanceLevel,
    required List<String> regionAccess,
    required bool canActivateDisasters,
  }) async {
    try {
      await _client.from('government_users').insert({
        'user_id': userId,
        'department': department,
        'clearance_level': clearanceLevel,
        'region_access': regionAccess,
        'can_activate_disasters': canActivateDisasters,
      });

      return true;
    } catch (e) {
      print('Error creating government user: $e');
      return false;
    }
  }

  /// Update camp information
  static Future<bool> updateCamp(Camp camp) async {
    try {
      await _client
          .from('rescue_camps')
          .update(camp.toJson())
          .eq('id', camp.id);

      return true;
    } catch (e) {
      print('Error updating camp: $e');
      return false;
    }
  }

  /// Create new camp
  static Future<Camp?> createCamp({
    required String name,
    required String address,
    required String type,
    required int capacity,
    required double latitude,
    required double longitude,
    required List<String> facilities,
    required Map<String, dynamic> contactInfo,
    List<String> accessibilityFeatures = const [],
    Map<String, dynamic> operatingHours = const {},
  }) async {
    try {
      final response = await _client
          .from('rescue_camps')
          .insert({
            'name': name,
            'address': address,
            'type': type,
            'capacity': capacity,
            'location': 'POINT($longitude $latitude)',
            'facilities': facilities,
            'contact_info': contactInfo,
            'accessibility_features': accessibilityFeatures,
            'operating_hours': operatingHours,
            'resource_levels': {'food': 100, 'water': 100, 'medical': 100, 'shelter': 100},
          })
          .select('*, ST_AsText(location) as location')
          .single();

      return Camp.fromJson(response);
    } catch (e) {
      print('Error creating camp: $e');
      return null;
    }
  }

  /// Update camp resources
  static Future<bool> updateCampResources(String campId, Map<String, int> resourceLevels) async {
    try {
      await _client
          .from('rescue_camps')
          .update({
            'resource_levels': resourceLevels,
            'last_updated': DateTime.now().toIso8601String(),
          })
          .eq('id', campId);

      return true;
    } catch (e) {
      print('Error updating camp resources: $e');
      return false;
    }
  }

  /// Update camp occupancy
  static Future<bool> updateCampOccupancy(String campId, int occupancy) async {
    try {
      await _client
          .from('rescue_camps')
          .update({
            'current_occupancy': occupancy,
            'last_updated': DateTime.now().toIso8601String(),
          })
          .eq('id', campId);

      return true;
    } catch (e) {
      print('Error updating camp occupancy: $e');
      return false;
    }
  }

  /// Create government alert
  static Future<bool> createAlert({
    required String title,
    required String message,
    required String alertType,
    required String severity,
    String? targetArea,
    DateTime? expiresAt,
  }) async {
    try {
      await _client.from('government_alerts').insert({
        'title': title,
        'message': message,
        'alert_type': alertType,
        'severity': severity,
        'target_area': targetArea,
        'expires_at': expiresAt?.toIso8601String(),
      });

      // Send immediate notification for critical alerts
      if (severity == 'critical' || severity == 'urgent') {
        await AlertService.sendEmergencyAlert(
          title: title,
          message: message,
          disasterType: alertType,
        );
      }

      return true;
    } catch (e) {
      print('Error creating alert: $e');
      return false;
    }
  }

  /// Get all government alerts
  static Future<List<alerts.GovernmentAlert>> getGovernmentAlerts() async {
    try {
      final response = await _client
          .from('government_alerts')
          .select('*')
          .order('created_at', ascending: false);

      return response.map<alerts.GovernmentAlert>((json) => alerts.GovernmentAlert.fromJson(json)).toList();
    } catch (e) {
      print('Error getting government alerts: $e');
      return [];
    }
  }

  /// Deactivate alert
  static Future<bool> deactivateAlert(String alertId) async {
    try {
      await _client
          .from('government_alerts')
          .update({'is_active': false})
          .eq('id', alertId);

      return true;
    } catch (e) {
      print('Error deactivating alert: $e');
      return false;
    }
  }

  /// Get emergency facilities
  static Future<List<EmergencyFacility>> getEmergencyFacilities() async {
    try {
      final response = await _client
          .from('emergency_facilities')
          .select('*, ST_AsText(location) as location')
          .order('name');

      return response.map<EmergencyFacility>((json) => EmergencyFacility.fromJson(json)).toList();
    } catch (e) {
      print('Error getting emergency facilities: $e');
      return [];
    }
  }

  /// Update emergency facility status
  static Future<bool> updateFacilityStatus(String facilityId, String status, int? capacity) async {
    try {
      await _client
          .from('emergency_facilities')
          .update({
            'operational_status': status,
            'current_capacity': capacity,
            'last_status_update': DateTime.now().toIso8601String(),
          })
          .eq('id', facilityId);

      return true;
    } catch (e) {
      print('Error updating facility status: $e');
      return false;
    }
  }

  /// Manually activate disaster
  static Future<bool> activateDisaster({
    required String type,
    required double latitude,
    required double longitude,
    required String description,
    double? magnitude,
    int? radiusKm,
  }) async {
    try {
      return await DisasterDetectionService.manuallyActivateDisaster(
        type: type,
        latitude: latitude,
        longitude: longitude,
        description: description,
        magnitude: magnitude,
        radiusKm: radiusKm,
      );
    } catch (e) {
      print('Error activating disaster: $e');
      return false;
    }
  }

  /// Get all active disasters
  static Future<List<Map<String, dynamic>>> getActiveDisasters() async {
    try {
      final response = await _client
          .from('disaster_events')
          .select('*')
          .eq('is_active', true)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('Error getting active disasters: $e');
      return [];
    }
  }

  /// Deactivate disaster
  static Future<bool> deactivateDisaster(String disasterId) async {
    try {
      await DisasterDetectionService.deactivateDisaster(disasterId);
      return true;
    } catch (e) {
      print('Error deactivating disaster: $e');
      return false;
    }
  }

  /// Get user locations during emergency
  static Future<List<Map<String, dynamic>>> getUserLocations() async {
    try {
      final response = await _client
          .from('live_user_locations')
          .select('''
            *,
            users!inner(name, phone)
          ''')
          .order('last_updated', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('Error getting user locations: $e');
      return [];
    }
  }

  /// Get dashboard statistics
  static Future<Map<String, dynamic>> getDashboardStats() async {
    try {
      // Get counts for various entities
      final activeCamps = await _client
          .from('rescue_camps')
          .select('id')
          .eq('is_active', true);

      final activeDisasters = await _client
          .from('disaster_events')
          .select('id')
          .eq('is_active', true);

      final activeAlerts = await _client
          .from('government_alerts')
          .select('id')
          .eq('is_active', true);

      final trackedUsers = await _client
          .from('live_user_locations')
          .select('user_id')
          .gte('last_updated', DateTime.now().subtract(const Duration(hours: 1)).toIso8601String());

      return {
        'active_camps': activeCamps.length,
        'active_disasters': activeDisasters.length,
        'active_alerts': activeAlerts.length,
        'tracked_users': trackedUsers.length,
      };
    } catch (e) {
      print('Error getting dashboard stats: $e');
      return {
        'active_camps': 0,
        'active_disasters': 0,
        'active_alerts': 0,
        'tracked_users': 0,
      };
    }
  }

  /// Send mass alert to all users
  static Future<bool> sendMassAlert({
    required String title,
    required String message,
    required String severity,
  }) async {
    try {
      await createAlert(
        title: title,
        message: message,
        alertType: 'safety_warning',
        severity: severity,
        expiresAt: DateTime.now().add(const Duration(hours: 24)),
      );

      return true;
    } catch (e) {
      print('Error sending mass alert: $e');
      return false;
    }
  }

  static Future<Map<String, dynamic>> getDashboardData() async {
    try {
      final govUser = await getCurrentGovernmentUser();
      if (govUser == null) {
        throw Exception('Not authorized as government user');
      }

      final disasters = await _client
          .from('disaster_events')
          .select('*')
          .eq('is_active', true)
          .count();

      final camps = await _client
          .from('relief_camps')
          .select('*')
          .eq('is_active', true)
          .count();

      final alerts = await _client
          .from('alerts')
          .select('*')
          .eq('is_active', true)
          .count();

      final peopleInDanger = await _client
          .from('user_statuses')
          .select('*')
          .neq('status', 'safe')
          .count();

      return {
        'active_disasters': disasters.count ?? 0,
        'active_camps': camps.count ?? 0,
        'alerts': alerts.count ?? 0,
        'people_in_danger': peopleInDanger.count ?? 0,
      };
    } catch (e) {
      print('Error getting dashboard data: $e');
      return {
        'active_disasters': 0,
        'active_camps': 0,
        'alerts': 0,
        'people_in_danger': 0,
      };
    }
  }

  static Future<List<Map<String, dynamic>>> getActiveCamps() async {
    try {
      final response = await _client
          .from('relief_camps')
          .select('*')
          .eq('is_active', true)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('Error getting active camps: $e');
      return [];
    }
  }

  static Future<List<Map<String, dynamic>>> getActiveAlerts() async {
    try {
      final response = await _client
          .from('alerts')
          .select('*')
          .eq('is_active', true)
          .order('created_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('Error getting active alerts: $e');
      return [];
    }
  }

  static Future<List<Map<String, dynamic>>> getPeopleInDanger() async {
    try {
      final response = await _client
          .from('user_statuses')
          .select('*, users(*)')
          .neq('status', 'safe')
          .order('updated_at', ascending: false);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('Error getting people in danger: $e');
      return [];
    }
  }

  static Future<bool> createDisaster({
    required String type,
    required String description,
    required double latitude,
    required double longitude,
    required double magnitude,
    required double radiusKm,
  }) async {
    try {
      final govUser = await getCurrentGovernmentUser();
      if (govUser == null) return false;

      await _client.from('disaster_events').insert({
        'type': type,
        'description': description,
        'latitude': latitude,
        'longitude': longitude,
        'magnitude': magnitude,
        'radius_km': radiusKm,
        'is_active': true,
        'created_by': govUser.id,
      });

      return true;
    } catch (e) {
      print('Error creating disaster: $e');
      return false;
    }
  }

  static Future<bool> createReliefCamp({
    required String name,
    required String address,
    required double latitude,
    required double longitude,
    required int capacity,
    required Map<String, dynamic> facilities,
  }) async {
    try {
      final govUser = await getCurrentGovernmentUser();
      if (govUser == null) return false;

      await _client.from('relief_camps').insert({
        'name': name,
        'address': address,
        'latitude': latitude,
        'longitude': longitude,
        'capacity': capacity,
        'facilities': facilities,
        'is_active': true,
        'created_by': govUser.id,
      });

      return true;
    } catch (e) {
      print('Error creating camp: $e');
      return false;
    }
  }

  static Future<bool> createEmergencyAlert({
    required String title,
    required String message,
    required String severity,
    required List<String> targetAreas,
    required DateTime expiresAt,
  }) async {
    try {
      final govUser = await getCurrentGovernmentUser();
      if (govUser == null) return false;

      await _client.from('alerts').insert({
        'title': title,
        'message': message,
        'severity': severity,
        'target_areas': targetAreas,
        'expires_at': expiresAt.toIso8601String(),
        'is_active': true,
        'created_by': govUser.id,
      });

      return true;
    } catch (e) {
      print('Error creating alert: $e');
      return false;
    }
  }

  static Future<bool> updateDisasterStatus(String disasterId, bool isActive) async {
    try {
      final govUser = await getCurrentGovernmentUser();
      if (govUser == null) return false;

      await _client.from('disaster_events').update({
        'is_active': isActive,
        'updated_by': govUser.id,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', disasterId);

      return true;
    } catch (e) {
      print('Error updating disaster status: $e');
      return false;
    }
  }

  static Future<bool> updateCampStatus(String campId, bool isActive) async {
    try {
      final govUser = await getCurrentGovernmentUser();
      if (govUser == null) return false;

      await _client.from('relief_camps').update({
        'is_active': isActive,
        'updated_by': govUser.id,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', campId);

      return true;
    } catch (e) {
      print('Error updating camp status: $e');
      return false;
    }
  }

  static Future<bool> updateAlertStatus(String alertId, bool isActive) async {
    try {
      final govUser = await getCurrentGovernmentUser();
      if (govUser == null) return false;

      await _client.from('alerts').update({
        'is_active': isActive,
        'updated_by': govUser.id,
        'updated_at': DateTime.now().toIso8601String(),
      }).eq('id', alertId);

      return true;
    } catch (e) {
      print('Error updating alert status: $e');
      return false;
    }
  }

  static Future<List<Map<String, dynamic>>> getRecentUserLocations(String userId) async {
    try {
      final response = await _client
          .from('user_locations')
          .select('*')
          .eq('user_id', userId)
          .order('timestamp', ascending: false)
          .limit(100);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('Error getting user locations: $e');
      return [];
    }
  }

  static Future<Map<String, dynamic>?> getUserStatus(String userId) async {
    try {
      final response = await _client
          .from('user_statuses')
          .select('*')
          .eq('user_id', userId)
          .single();

      return response as Map<String, dynamic>?;
    } catch (e) {
      print('Error getting user status: $e');
      return null;
    }
  }

  static Future<List<Map<String, dynamic>>> searchUsers(String query) async {
    try {
      final response = await _client
          .from('users')
          .select('*')
          .or('name.ilike.%$query%,phone.ilike.%$query%')
          .limit(20);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('Error searching users: $e');
      return [];
    }
  }
}
