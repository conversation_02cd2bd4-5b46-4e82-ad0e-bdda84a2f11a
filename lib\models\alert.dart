class Alert {
  final String id;
  final String title;
  final String message;
  final String severity; // 'info', 'warning', 'urgent', 'critical'

  <PERSON><PERSON>({
    required this.id,
    required this.title,
    required this.message,
    required this.severity,
  });

  // Mock alerts
  static List<Alert> mockList() => [
    <PERSON><PERSON>(id: '1', title: 'Evacuation Order', message: 'Evacuate Zone A immediately!', severity: 'critical'),
    <PERSON><PERSON>(id: '2', title: 'Shelter Info', message: 'New shelter opened at School #5.', severity: 'info'),
    <PERSON><PERSON>(id: '3', title: 'Resource Update', message: 'Water supply low in Camp Bravo.', severity: 'warning'),
  ];
} 