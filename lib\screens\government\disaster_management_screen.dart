import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../services/government_service.dart';
import '../../models/disaster.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class DisasterManagementScreen extends ConsumerWidget {
  const DisasterManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Disaster Management'),
      ),
      body: const Center(
        child: Text('Disaster Management Screen'),
      ),
    );
  }
}

class _DisasterManagementScreenState extends State<DisasterManagementScreen> {
  bool isLoading = false;
  List<Map<String, dynamic>> disasters = [];
  final Set<Marker> _markers = {};
  final Set<Circle> _circles = {};
  GoogleMapController? _mapController;

  @override
  void initState() {
    super.initState();
    _loadDisasters();
  }

  Future<void> _loadDisasters() async {
    setState(() => isLoading = true);

    try {
      final activeDisasters = await GovernmentService.getActiveDisasters();
      setState(() {
        disasters = activeDisasters;
        _updateMapMarkers();
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }

    setState(() => isLoading = false);
  }

  void _updateMapMarkers() {
    _markers.clear();
    _circles.clear();

    for (var disaster in disasters) {
      final LatLng position = LatLng(
        disaster['latitude'] as double,
        disaster['longitude'] as double,
      );

      _markers.add(
        Marker(
          markerId: MarkerId(disaster['id'].toString()),
          position: position,
          infoWindow: InfoWindow(
            title: disaster['type'],
            snippet: disaster['description'],
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(
            disaster['type'] == 'earthquake'
                ? BitmapDescriptor.hueRed
                : BitmapDescriptor.hueOrange,
          ),
        ),
      );

      _circles.add(
        Circle(
          circleId: CircleId(disaster['id'].toString()),
          center: position,
          radius: (disaster['radius_km'] as double) * 1000, // Convert km to meters
          fillColor: Colors.red.withOpacity(0.2),
          strokeColor: Colors.red,
          strokeWidth: 2,
        ),
      );
    }

    if (_mapController != null && disasters.isNotEmpty) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(
          _getBounds(_markers.map((m) => m.position).toList()),
          50.0,
        ),
      );
    }
  }

  LatLngBounds _getBounds(List<LatLng> points) {
    double minLat = points.first.latitude;
    double maxLat = points.first.latitude;
    double minLng = points.first.longitude;
    double maxLng = points.first.longitude;

    for (var point in points) {
      if (point.latitude < minLat) minLat = point.latitude;
      if (point.latitude > maxLat) maxLat = point.latitude;
      if (point.longitude < minLng) minLng = point.longitude;
      if (point.longitude > maxLng) maxLng = point.longitude;
    }

    return LatLngBounds(
      southwest: LatLng(minLat, minLng),
      northeast: LatLng(maxLat, maxLng),
    );
  }

  Future<void> _addDisaster() async {
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => const _AddDisasterDialog(),
    );

    if (result != null) {
      setState(() => isLoading = true);

      try {
        final success = await GovernmentService.createDisaster(
          type: result['type'],
          description: result['description'],
          latitude: result['latitude'],
          longitude: result['longitude'],
          magnitude: result['magnitude'],
          radiusKm: result['radius_km'],
        );

        if (success) {
          await _loadDisasters();
        } else {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Failed to create disaster')),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: $e')),
          );
        }
      }

      setState(() => isLoading = false);
    }
  }

  Future<void> _updateDisasterStatus(String disasterId, bool isActive) async {
    setState(() => isLoading = true);

    try {
      final success = await GovernmentService.updateDisasterStatus(disasterId, isActive);
      if (success) {
        await _loadDisasters();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to update disaster status')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }

    setState(() => isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Disaster Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDisasters,
          ),
        ],
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                SizedBox(
                  height: 300,
                  child: GoogleMap(
                    initialCameraPosition: const CameraPosition(
                      target: LatLng(20.5937, 78.9629), // Center of India
                      zoom: 5,
                    ),
                    markers: _markers,
                    circles: _circles,
                    onMapCreated: (controller) => _mapController = controller,
                  ),
                ),
                Expanded(
                  child: disasters.isEmpty
                      ? const Center(
                          child: Text(
                            'No active disasters',
                            style: TextStyle(fontSize: 16),
                          ),
                        )
                      : ListView.builder(
                          itemCount: disasters.length,
                          itemBuilder: (context, index) {
                            final disaster = disasters[index];
                            return Card(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              child: ListTile(
                                leading: Icon(
                                  disaster['type'] == 'earthquake'
                                      ? Icons.warning
                                      : Icons.water_drop,
                                  color: Colors.red,
                                ),
                                title: Text(
                                  '${disaster['type'][0].toUpperCase()}${disaster['type'].substring(1)}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(disaster['description']),
                                    Text(
                                      'Magnitude: ${disaster['magnitude']}',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                                trailing: IconButton(
                                  icon: const Icon(Icons.check_circle),
                                  color: Colors.green,
                                  onPressed: () => _updateDisasterStatus(
                                    disaster['id'].toString(),
                                    false,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addDisaster,
        child: const Icon(Icons.add),
      ),
    );
  }
}

class _AddDisasterDialog extends StatefulWidget {
  const _AddDisasterDialog();

  @override
  State<_AddDisasterDialog> createState() => _AddDisasterDialogState();
}

class _AddDisasterDialogState extends State<_AddDisasterDialog> {
  final _formKey = GlobalKey<FormState>();
  String _type = 'earthquake';
  final _descriptionController = TextEditingController();
  final _latitudeController = TextEditingController();
  final _longitudeController = TextEditingController();
  final _magnitudeController = TextEditingController();
  final _radiusController = TextEditingController();

  @override
  void dispose() {
    _descriptionController.dispose();
    _latitudeController.dispose();
    _longitudeController.dispose();
    _magnitudeController.dispose();
    _radiusController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Disaster'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DropdownButtonFormField<String>(
                value: _type,
                decoration: const InputDecoration(labelText: 'Type'),
                items: const [
                  DropdownMenuItem(
                    value: 'earthquake',
                    child: Text('Earthquake'),
                  ),
                  DropdownMenuItem(
                    value: 'flood',
                    child: Text('Flood'),
                  ),
                ],
                onChanged: (value) => setState(() => _type = value!),
              ),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(labelText: 'Description'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a description';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _latitudeController,
                decoration: const InputDecoration(labelText: 'Latitude'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter latitude';
                  }
                  final latitude = double.tryParse(value);
                  if (latitude == null || latitude < -90 || latitude > 90) {
                    return 'Invalid latitude';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _longitudeController,
                decoration: const InputDecoration(labelText: 'Longitude'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter longitude';
                  }
                  final longitude = double.tryParse(value);
                  if (longitude == null || longitude < -180 || longitude > 180) {
                    return 'Invalid longitude';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _magnitudeController,
                decoration: const InputDecoration(labelText: 'Magnitude'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter magnitude';
                  }
                  final magnitude = double.tryParse(value);
                  if (magnitude == null || magnitude <= 0) {
                    return 'Invalid magnitude';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _radiusController,
                decoration: const InputDecoration(labelText: 'Radius (km)'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter radius';
                  }
                  final radius = double.tryParse(value);
                  if (radius == null || radius <= 0) {
                    return 'Invalid radius';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              Navigator.pop(context, {
                'type': _type,
                'description': _descriptionController.text,
                'latitude': double.parse(_latitudeController.text),
                'longitude': double.parse(_longitudeController.text),
                'magnitude': double.parse(_magnitudeController.text),
                'radius_km': double.parse(_radiusController.text),
              });
            }
          },
          child: const Text('Add'),
        ),
      ],
    );
  }
}
